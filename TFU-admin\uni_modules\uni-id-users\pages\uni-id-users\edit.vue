<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="avatar_file" label="头像文件">
				<uni-file-picker return-type="object" v-model="formData.avatar_file"></uni-file-picker>
			</uni-forms-item>
			<uni-forms-item name="department_id" label="部门">
				<department-multi-select v-model="formData.department_id" placeholder="请选择部门"
					@change="handleDepartmentChange">
				</department-multi-select>
			</uni-forms-item>

			<uni-forms-item name="email" label="邮箱">
				<uni-easyinput placeholder="邮箱地址" v-model="formData.email" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="gender" label="性别">
				<uni-data-checkbox v-model="formData.gender"
					:localdata="formOptions.gender_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="mobile" label="手机号码">
				<uni-easyinput placeholder="手机号码" v-model="formData.mobile" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="nickname" label="昵称">
				<uni-easyinput placeholder="用户昵称" v-model="formData.nickname" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="role" label="角色">
				<uni-data-picker :multiple="true" v-model="TempData.role" collection="uni-id-roles"
					field="role_id as value, role_name as text" @change="changeRole"></uni-data-picker>
			</uni-forms-item>
			<uni-forms-item name="tags" label="标签">
				<uni-data-checkbox :multiple="true" v-model="formData.tags"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="status" label="用户状态">
				<uni-data-checkbox v-model="formData.status"
					:localdata="formOptions.status_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="username" label="用户名">
				<uni-easyinput placeholder="用户名，不允许重复" v-model="formData.username" trim="both"></uni-easyinput>
			</uni-forms-item>
			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/uni-id-users.js';
	import DepartmentMultiSelect from '../../components/department-multi-select.vue';

	const db = uniCloud.database();
	const dbCmd = db.command;
	const dbCollectionID = 'uni-id-users';

	function getValidator(fields) {
		let result = {}
		for (let key in validator) {
			if (fields.includes(key)) {
				result[key] = validator[key]
			}
		}
		return result
	}

	export default {
		components: {
			DepartmentMultiSelect
		},
		data() {
			let TempData = {
				"role": ""
			}
			let formData = {
				"avatar_file": null,
				"department_id": [],
				"email": "",
				"gender": 0,
				"mobile": "",
				"nickname": "",
				"role": [],
				"tags": [],
				"status": 0,
				"username": ""
			}
			return {
				TempData,
				formData,
				formOptions: {
					"gender_localdata": [{
							"text": "未知",
							"value": 0
						},
						{
							"text": "男",
							"value": 1
						},
						{
							"text": "女",
							"value": 2
						}
					],
					"status_localdata": [{
							"text": "正常",
							"value": 0
						},
						{
							"text": "禁用",
							"value": 1
						},
						{
							"text": "审核中",
							"value": 2
						},
						{
							"text": "审核拒绝",
							"value": 3
						},
						{
							"text": "已注销",
							"value": 4
						}
					]
				},
				rules: {
					...getValidator(Object.keys(formData))
				}
			}
		},
		onLoad(e) {
			if (e.id) {
				const id = e.id
				this.formDataId = id
				this.getDetail(id)
			}
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},
		methods: {
			changeRole(e) {
				const selectedRoles = e.detail.value; // 获取 detail 中的选中角色数据
				console.log(e)
				if (Array.isArray(selectedRoles)) {
					this.formData.role = selectedRoles.map(item => item.value); // 直接赋值为数组
				} else {
					console.error("Invalid role data:", selectedRoles);
					uni.showToast({
						title: '选择的角色数据无效',
						icon: 'none'
					});
				}
			},

			/**
			 * 处理部门选择变化
			 */
			handleDepartmentChange(e) {
				try {
					if (Array.isArray(e)) {
						this.formData.department_id = e;
						uni.showToast({
							title: `已选择${e.length}个部门`,
							icon: 'success',
							duration: 1500
						});
					} else {
						console.error('部门数据格式错误:', e);
						uni.showToast({
							title: '选择部门失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('处理部门选择失败:', error);
					uni.showToast({
						title: '选择部门失败',
						icon: 'none'
					});
				}
			},



			/**
			 * 验证表单并提交
			 */
			submit() {
				uni.showLoading({
					mask: true
				})



				this.$refs.form.validate().then((res) => {
					return this.submitForm(res)
				}).catch(() => {}).finally(() => {
					uni.hideLoading()
				})
			},

			/**
			 * 提交表单
			 */
			submitForm(value) {
				// 使用 clientDB 提交数据
				return db.collection(dbCollectionID).doc(this.formDataId).update(value).then((res) => {
					uni.showToast({
						title: '修改成功'
					})
					this.getOpenerEventChannel().emit('refreshData')
					setTimeout(() => uni.navigateBack(), 500)
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				})
			},

			/**
			 * 获取表单数据
			 * @param {Object} id
			 */
			getDetail(id) {
				uni.showLoading({
					mask: true
				})
				db.collection(dbCollectionID).doc(id).field(
					"avatar_file,department_id,email,gender,mobile,nickname,role,tags,status,username"
				).get().then((res) => {
					const data = res.result.data[0]
					if (data) {
						this.formData = data
						// 确保department_id是数组格式
						if (!Array.isArray(this.formData.department_id)) {
							this.formData.department_id = this.formData.department_id ? [this.formData
								.department_id
							] : [];
						}
						this.TempData.role = this.formData.role[0];
						console.log('Department IDs:', this.formData.department_id);
						console.log('First role:', this.TempData.role);
					}
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				}).finally(() => {
					uni.hideLoading()
				})
			}
		}
	}
</script>