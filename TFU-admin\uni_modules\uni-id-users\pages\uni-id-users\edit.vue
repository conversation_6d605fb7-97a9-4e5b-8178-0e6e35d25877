<template>
	<view class="uni-container">
		<uni-forms ref="form" :model="formData" validateTrigger="bind">
			<uni-forms-item name="avatar_file" label="头像文件">
				<uni-file-picker return-type="object" v-model="formData.avatar_file"></uni-file-picker>
			</uni-forms-item>
			<uni-forms-item name="department_id" label="部门">
				<uni-data-checkbox
					v-model="formData.department_id"
					collection="opendb-department"
					field="_id as value, name as text"
					orderby="name asc"
					:multiple="true"
					mode="tag"
					@change="handleDepartmentChange">
				</uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="shifts_id" label="排版计划">
				<uni-data-picker v-model="TempData.shifts_id"
					collection="shifts_Scheduling_Management" field="_id as value, name as text" orderby="name asc"
					:multiple="false" @change="changeShifts">
				</uni-data-picker>
				<view class="button-group" style="margin-top: 10px; display: flex;">
					<button type="primary" size="mini" @click="createSchedule" style="margin-right: 10px;">创建排班计划</button>
					<button type="default" size="mini" @click="viewSchedule">查看排班计划</button>
				</view>
			</uni-forms-item>
			<uni-forms-item name="email" label="邮箱">
				<uni-easyinput placeholder="邮箱地址" v-model="formData.email" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="gender" label="性别">
				<uni-data-checkbox v-model="formData.gender"
					:localdata="formOptions.gender_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="mobile" label="手机号码">
				<uni-easyinput placeholder="手机号码" v-model="formData.mobile" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="nickname" label="昵称">
				<uni-easyinput placeholder="用户昵称" v-model="formData.nickname" trim="both"></uni-easyinput>
			</uni-forms-item>
			<uni-forms-item name="role" label="角色">
				<uni-data-picker :multiple="true" v-model="TempData.role" collection="uni-id-roles"
					field="role_id as value, role_name as text" @change="changeRole"></uni-data-picker>
			</uni-forms-item>
			<uni-forms-item name="tags" label="标签">
				<uni-data-checkbox :multiple="true" v-model="formData.tags"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="status" label="用户状态">
				<uni-data-checkbox v-model="formData.status"
					:localdata="formOptions.status_localdata"></uni-data-checkbox>
			</uni-forms-item>
			<uni-forms-item name="username" label="用户名">
				<uni-easyinput placeholder="用户名，不允许重复" v-model="formData.username" trim="both"></uni-easyinput>
			</uni-forms-item>
			<view class="uni-button-group">
				<button type="primary" class="uni-button" style="width: 100px;" @click="submit">提交</button>
				<navigator open-type="navigateBack" style="margin-left: 15px;">
					<button class="uni-button" style="width: 100px;">返回</button>
				</navigator>
			</view>
		</uni-forms>
	</view>
</template>

<script>
	import {
		validator
	} from '../../js_sdk/validator/uni-id-users.js';

	const db = uniCloud.database();
	const dbCmd = db.command;
	const dbCollectionID = 'uni-id-users';
	const dbCollectionShifts = 'shifts_Scheduling_Management';
	const dbCollectionUserShifts = 'user_shifts_scheduling';
	
	function getValidator(fields) {
		let result = {}
		for (let key in validator) {
			if (fields.includes(key)) {
				result[key] = validator[key]
			}
		}
		return result
	}

	export default {
		data() {
			let TempData = {
				"role": "",
				"shifts_id": ""
			}
			let formData = {
				"avatar_file": null,
				"department_id": [],
				"email": "",
				"gender": 0,
				"mobile": "",
				"nickname": "",
				"role": [],
				"tags": [],
				"status": 0,
				"username": "",
				"shifts_id": ""
			}
			return {
				TempData,
				formData,
				formOptions: {
					"gender_localdata": [{
							"text": "未知",
							"value": 0
						},
						{
							"text": "男",
							"value": 1
						},
						{
							"text": "女",
							"value": 2
						}
					],
					"status_localdata": [{
							"text": "正常",
							"value": 0
						},
						{
							"text": "禁用",
							"value": 1
						},
						{
							"text": "审核中",
							"value": 2
						},
						{
							"text": "审核拒绝",
							"value": 3
						},
						{
							"text": "已注销",
							"value": 4
						}
					]
				},
				rules: {
					...getValidator(Object.keys(formData))
				}
			}
		},
		onLoad(e) {
			if (e.id) {
				const id = e.id
				this.formDataId = id
				this.getDetail(id)
			}
		},
		onReady() {
			this.$refs.form.setRules(this.rules)
		},
		methods: {
			changeRole(e) {
				const selectedRoles = e.detail.value; // 获取 detail 中的选中角色数据

				if (Array.isArray(selectedRoles)) {
					this.formData.role = selectedRoles.map(item => item.value); // 直接赋值为数组
				} else {
					console.error("Invalid role data:", selectedRoles);
					uni.showToast({
						title: '选择的角色数据无效',
						icon: 'none'
					});
				}
			},

			/**
			 * 处理部门选择变化
			 */
			handleDepartmentChange(e) {
				try {
					if (Array.isArray(e)) {
						this.formData.department_id = e;
						uni.showToast({
							title: `已选择${e.length}个部门`,
							icon: 'success',
							duration: 1500
						});
					} else {
						console.error('部门数据格式错误:', e);
						uni.showToast({
							title: '选择部门失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('处理部门选择失败:', error);
					uni.showToast({
						title: '选择部门失败',
						icon: 'none'
					});
				}
			},
			
			changeShifts(e) {
				const selectedShifts = e.detail.value; // 获取 detail 中的选中排版计划数据
				if (Array.isArray(selectedShifts) && selectedShifts.length > 0) {
					// 如果是数组，取第一个元素的value
					this.formData.shifts_id = selectedShifts[0].value;
				} else if (selectedShifts && selectedShifts.value) {
					// 如果是单个对象，直接获取value
					this.formData.shifts_id = selectedShifts.value;
				} else {
					// 处理空值或无效值情况
					this.formData.shifts_id = "";
				}
				console.log('选中的排班计划ID:', this.formData.shifts_id);
			},
			
			// 创建排班计划，跳转到添加页面并传递参数
			createSchedule() {
				if (!this.formData.shifts_id) {
					uni.showToast({
						title: '请先选择排版计划',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formDataId) {
					uni.showToast({
						title: '请先保存用户信息',
						icon: 'none'
					});
					return;
				}
				
				uni.navigateTo({
					url: '/uni_modules/user_shifts_scheduling/pages/user_shifts_scheduling/add',
					success: (res) => {
						// 页面打开成功后，向页面发送参数
						res.eventChannel.emit('params', {
							user_id: this.formDataId,
							shifts_id: this.formData.shifts_id
						});
					}
				});
			},
			
			// 查看排班计划
			viewSchedule() {
				if (!this.formData.shifts_id) {
					uni.showToast({
						title: '请先选择排版计划',
						icon: 'none'
					});
					return;
				}
				
				if (!this.formDataId) {
					uni.showToast({
						title: '请先保存用户信息',
						icon: 'none'
					});
					return;
				}
				
				// 直接跳转到排班计划页面并传递参数
				uni.navigateTo({
					url: '/uni_modules/user_shifts_scheduling/pages/user_shifts_scheduling/edit',
					success: (res) => {
						// 页面打开成功后，向页面发送参数
						res.eventChannel.emit('params', {
							user_id: this.formDataId,
							shifts_id: this.formData.shifts_id
						});
					}
				});
			},
			
			/**
			 * 验证表单并提交
			 */
			submit() {
				uni.showLoading({
					mask: true
				})
				
				
				
				this.$refs.form.validate().then((res) => {
					return this.submitForm(res)
				}).catch(() => {}).finally(() => {
					uni.hideLoading()
				})
			},

			/**
			 * 提交表单
			 */
			submitForm(value) {
				// 使用 clientDB 提交数据
				return db.collection(dbCollectionID).doc(this.formDataId).update(value).then((res) => {
					uni.showToast({
						title: '修改成功'
					})
					this.getOpenerEventChannel().emit('refreshData')
					setTimeout(() => uni.navigateBack(), 500)
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				})
			},

			/**
			 * 获取表单数据
			 * @param {Object} id
			 */
			getDetail(id) {
				uni.showLoading({
					mask: true
				})
				db.collection(dbCollectionID).doc(id).field(
					"avatar_file,department_id,email,gender,mobile,nickname,role,tags,status,username,shifts_id"
				).get().then((res) => {
					const data = res.result.data[0]
					if (data) {
						this.formData = data
						// 确保department_id是数组格式
						if (!Array.isArray(this.formData.department_id)) {
							this.formData.department_id = this.formData.department_id ? [this.formData.department_id] : [];
						}
						this.TempData.role = this.formData.role[0];
						if (this.formData.shifts_id) {
							this.TempData.shifts_id = this.formData.shifts_id;
						}
						console.log('Department IDs:', this.formData.department_id);
						console.log('First role:', this.TempData.role);
						console.log('Shifts ID:', this.TempData.shifts_id);
					}
				}).catch((err) => {
					uni.showModal({
						content: err.message || '请求服务失败',
						showCancel: false
					})
				}).finally(() => {
					uni.hideLoading()
				})
			}
		}
	}
</script>