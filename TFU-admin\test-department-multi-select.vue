<template>
  <view class="container">
    <uni-card title="部门多选下拉测试页面" is-full>
      <text class="description">
        测试自定义部门多选下拉组件的功能
      </text>
    </uni-card>
    
    <uni-section title="部门多选下拉组件测试" type="line">
      <uni-forms ref="form" :model="formData">
        <uni-forms-item name="department_id" label="选择部门">
          <department-multi-select
            v-model="formData.department_id"
            placeholder="请选择部门"
            @change="handleDepartmentChange">
          </department-multi-select>
        </uni-forms-item>
      </uni-forms>
    </uni-section>
    
    <uni-section title="选择结果" type="line">
      <view class="result-container">
        <text class="result-title">已选择的部门ID：</text>
        <text class="result-content">{{ JSON.stringify(formData.department_id) }}</text>
        
        <text class="result-title">选择数量：</text>
        <text class="result-content">{{ formData.department_id.length }} 个部门</text>
      </view>
    </uni-section>
    
    <view class="button-group">
      <button type="primary" @click="testSubmit">测试提交</button>
      <button type="default" @click="clearSelection">清空选择</button>
    </view>
  </view>
</template>

<script>
import DepartmentMultiSelect from './uni_modules/uni-id-users/components/department-multi-select.vue'

export default {
  components: {
    DepartmentMultiSelect
  },
  data() {
    return {
      formData: {
        department_id: []
      }
    }
  },
  methods: {
    /**
     * 处理部门选择变化
     */
    handleDepartmentChange(e) {
      try {
        console.log('部门选择变化:', e);
        if (Array.isArray(e)) {
          this.formData.department_id = e;
          uni.showToast({
            title: `已选择${e.length}个部门`,
            icon: 'success',
            duration: 1500
          });
        } else {
          console.error('部门数据格式错误:', e);
          uni.showToast({
            title: '选择部门失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('处理部门选择失败:', error);
        uni.showToast({
          title: '选择部门失败',
          icon: 'none'
        });
      }
    },
    
    /**
     * 测试提交
     */
    testSubmit() {
      console.log('提交数据:', this.formData);
      uni.showModal({
        title: '提交测试',
        content: `选择了 ${this.formData.department_id.length} 个部门\n部门ID: ${JSON.stringify(this.formData.department_id)}`,
        showCancel: false
      });
    },
    
    /**
     * 清空选择
     */
    clearSelection() {
      this.formData.department_id = [];
      uni.showToast({
        title: '已清空选择',
        icon: 'success'
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
}

.description {
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
}

.result-container {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-top: 20rpx;
}

.result-title {
  display: block;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
  margin-top: 20rpx;
}

.result-title:first-child {
  margin-top: 0;
}

.result-content {
  display: block;
  color: #666;
  font-size: 28rpx;
  word-break: break-all;
}

.button-group {
  display: flex;
  justify-content: space-around;
  margin-top: 40rpx;
  padding: 0 40rpx;
}

.button-group button {
  flex: 1;
  margin: 0 10rpx;
}
</style>
