---
type: "always_apply"
description: "uni-app项目开发规范与最佳实践 - 每次代码修改前必须验证的规则"
---
# 🚨 代码修改前必须验证的核心规则

## 📖 使用说明

**本文档是 uni-app 项目的核心开发规范，每次修改代码前必须按照以下规则进行验证。**

### 🎯 使用方式
1. **修改前**：查看"快速检查清单"，确保满足所有验证项
2. **修改中**：参考"代码修改详细验证规则"中的示例
3. **修改后**：按照"代码修改验证流程"进行质量检查
4. **遇到问题**：查看"常见问题解决方案"寻找答案

### ⚠️ 重要提醒
- **组件选择优先级**：优先使用 uv-ui 和 uCharts，其次使用拓展组件和内置组件
- **测试策略**：uni-app 框架在 VSCode 中无法正常测试，不需要编写测试脚本
- **文档策略**：开发阶段不需要编写总结文档，专注于功能实现
- 所有数据处理必须进行类型验证和错误处理
- 所有用户操作必须提供即时反馈
- 所有异步操作必须有完整的错误处理机制

## ⚡ 快速检查清单（每次修改前必须验证）

### 1. 组件选择验证
- [ ] **优先使用项目已集成的组件**：uv-ui、uCharts
- [ ] **其次使用拓展组件和内置组件**：uni-ui、uni-modules 等
- [ ] **避免重复造轮子**：检查是否已有现成组件
- [ ] **组件兼容性**：确保组件支持多选、数据绑定等需求
- [ ] **数据类型匹配**：v-model 绑定的数据类型必须与组件期望类型一致

### 2. 数据处理验证
- [ ] **数组初始化**：确保所有数组变量正确初始化为 `[]`
- [ ] **数据类型检查**：使用 `Array.isArray()` 验证数组类型
- [ ] **事件数据格式**：明确组件返回的数据格式（字符串/数组/对象）
- [ ] **数据同步**：确保 UI 显示数据与表单提交数据同步

### 3. 错误处理验证
- [ ] **异常捕获**：所有异步操作必须有 try-catch
- [ ] **用户反馈**：提供明确的成功/失败提示
- [ ] **数据验证**：验证用户输入和 API 返回数据
- [ ] **边界情况**：处理空数据、网络错误等边界情况

### 4. 性能优化验证
- [ ] **避免不必要的日志**：生产环境移除调试日志
- [ ] **事件防抖**：频繁触发的事件添加防抖处理
- [ ] **内存泄漏**：及时清理定时器、事件监听器
- [ ] **数据缓存**：合理使用缓存减少重复请求

### 5. 用户体验验证
- [ ] **加载状态**：提供 loading 提示
- [ ] **操作反馈**：即时反馈用户操作结果
- [ ] **界面响应**：确保界面操作流畅
- [ ] **错误恢复**：提供错误后的恢复机制
# 开发文档索引路径

> 主开发文档索引：doc-index.md

# 文档分类与索引规范

## 文档分类原则
- 所有项目相关文档应根据内容类型、业务领域、用途进行分类，便于查找和维护。
- 常见分类包括：开发规范、功能设计、接口文档、变更记录、测试用例、部署运维、FAQ等。

## 索引文件与路径组织
- 每一类文档应在项目根目录下建立对应的索引文件，命名格式为：`<分类>-index.md`，如：`doc-index.md`、`database-index.md`、`cloud-function-index.md`、`auth-security-index.md`。
- 各业务模块、子系统、重要功能建议在其目录下建立本地索引文件，便于模块内文档的快速导航。
- 所有索引文件应放在项目根目录或相关业务目录下，保持结构清晰。

## 索引内容规范
- 索引文件需以列表形式罗列本分类下所有文档，建议附带简要说明。
- 支持多级索引，主索引可引用子索引。
- 示例：
```markdown
- [开发规范](doc-index.md)
- [数据库设计](database-index.md)
- [云函数设计](cloud-function-index.md)
- [安全与认证](auth-security-index.md)
```

## 文档引用与跳转
- 文档间引用请使用相对路径，避免绝对路径或硬编码。
- 推荐在每个文档顶部添加“返回索引”链接，提升可导航性。
- 示例：
```markdown
[返回开发文档索引](../doc-index.md)
```

## 版本与归档
- 历史文档、废弃文档应归档至 `docs/archive/` 或类似目录，并在索引中标注。

---
# uni-app 项目开发规范

## 项目结构规范

### 目录组织
```
项目根目录/

┌─uniCloud              云空间目录，支付宝小程序云为uniCloud-alipay，阿里云为uniCloud-aliyun，腾讯云为uniCloud-tcb（详见uniCloud）
    ├── cloudfunctions/    # 云函数
   └── database/          # 数据库Schema
│─components            符合vue组件规范的uni-app组件目录
│  └─comp-a.vue         可复用的a组件
├─utssdk                存放uts文件（已废弃）
├─pages                 业务页面文件存放的目录
│  ├─index
│  │  └─index.vue       index页面
│  └─list
│     └─list.vue        list页面
├─static                存放应用引用的本地静态资源（如图片、视频等）的目录，注意：静态资源都应存放于此目录
├─uni_modules           存放uni_module 详见
├─platforms             存放各平台专用页面的目录，详见
├─nativeplugins         App原生语言插件 详见
├─nativeResources       App端原生资源目录
│  ├─android            Android原生资源目录 详见
|  └─ios                iOS原生资源目录 详见
├─hybrid                App端存放本地html文件的目录，详见
├─wxcomponents          存放微信小程序、QQ小程序组件的目录，详见
├─mycomponents          存放支付宝小程序组件的目录，详见
├─swancomponents        存放百度小程序组件的目录，详见
├─ttcomponents          存放抖音小程序、飞书小程序组件的目录，详见
├─kscomponents          存放快手小程序组件的目录，详见
├─jdcomponents          存放京东小程序组件的目录，详见
├─unpackage             非工程代码，一般存放运行或发行的编译结果
├─main.js               Vue初始化入口文件
├─App.vue               应用配置，用来配置App全局样式以及监听 应用生命周期
├─pages.json            配置页面路由、导航条、选项卡等页面类信息，详见
├─manifest.json         配置应用名称、appid、logo、版本等打包信息，详见
├─AndroidManifest.xml   Android原生应用清单文件 详见
├─Info.plist            iOS原生应用配置文件 详见
└─uni.scss              内置的常用样式变量
```

## 命名规范

### 文件命名
- **页面文件**：使用小写字母和连字符，如 `userProfile.vue`
- **组件文件**：使用大驼峰命名，如 `UserCard.vue`
- **工具文件**：使用小驼峰命名，如 `dateUtils.js`
- **样式文件**：使用小写字母和连字符，如 `common-styles.scss`

### 变量命名
- **data属性**：使用小驼峰命名，如 `userName`、`orderList`
- **方法名**：使用小驼峰命名，动词开头，如 `getUserInfo`、`handleSubmit`
- **常量**：使用大写字母和下划线，如 `API_BASE_URL`、`MAX_RETRY_COUNT`

### 组件命名
- **自定义组件**：使用大驼峰命名，如 `<UserCard>`、`<OrderItem>`
- **页面组件**：使用小写字母和连字符，如 `<user-profile>`

## 代码规范

### Vue组件结构
```vue
<template>
  <!-- 模板内容 -->
  <view class="container">
    <!-- 使用语义化的class名称 -->
  </view>
</template>

<script>
export default {
  name: 'ComponentName',
  
  // 组件属性按以下顺序排列
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      // 数据属性
    }
  },
  computed: {},
  watch: {},
  
  // 生命周期钩子按执行顺序排列
  onLoad() {},
  onShow() {},
  onReady() {},
  onHide() {},
  onUnload() {},
  
  methods: {
    // 方法按功能分组，相关方法放在一起
    
    // 事件处理方法
    handleClick() {},
    handleSubmit() {},
    
    // 数据获取方法
    getUserInfo() {},
    getOrderList() {},
    
    // 工具方法
    formatDate() {},
    validateForm() {}
  }
}
</script>

<style lang="scss" scoped>
/* 样式代码 */
.container {
  /* 使用语义化的选择器 */
}
</style>
```

### 云函数规范
```javascript
'use strict';

/**
 * 云函数名称：getUserInfo
 * 功能描述：获取用户信息
 * 参数：{ userId: string }
 * 返回：{ status: string, message: string, data?: object }
 */
exports.main = async (event, context) => {
  try {
    const { userId } = event;
    
    // 参数验证
    if (!userId) {
      return {
        status: 'error',
        message: '用户ID不能为空'
      };
    }
    
    // 业务逻辑
    const db = uniCloud.database();
    const userInfo = await db.collection('users')
      .doc(userId)
      .get();
    
    // 统一返回格式
    return {
      status: 'success',
      message: '获取用户信息成功',
      data: userInfo.data[0]
    };
    
  } catch (error) {
    console.error('getUserInfo error:', error);
    return {
      status: 'error',
      message: '获取用户信息失败'
    };
  }
};
```

## 🔍 代码修改详细验证规则

### 组件使用验证规则

#### 1. 表单组件选择规则
```javascript
// ❌ 错误：直接使用不兼容的组件
<uni-data-picker v-model="arrayData" :multiple="true" />
// 问题：uni-data-picker 的 v-model 不支持数组类型

// ✅ 正确：使用兼容的组件或自定义方案
<uni-data-select v-model="singleValue" @change="handleMultiSelect" />
// 配合自定义逻辑实现多选
```

#### 2. 数据绑定验证规则
```javascript
// ❌ 错误：数据类型不匹配
data() {
  return {
    selectedItems: "string" // 错误：应该是数组
  }
}

// ✅ 正确：正确的数据类型
data() {
  return {
    selectedItems: [], // 正确：数组类型
    currentSelectValue: '' // 单选值
  }
}
```

#### 3. 事件处理验证规则
```javascript
// ❌ 错误：未验证数据格式
onDataChange(e) {
  this.data = e.map(item => item.value) // 可能报错：e 不是数组
}

// ✅ 正确：验证数据格式
onDataChange(e) {
  if (Array.isArray(e)) {
    this.data = e.map(item => item.value)
  } else if (typeof e === 'string') {
    // 处理单个值的情况
    this.handleSingleValue(e)
  }
}
```

### 错误处理验证规则

#### 1. 异步操作必须有错误处理
```javascript
// ❌ 错误：没有错误处理
async loadData() {
  const res = await api.getData()
  this.data = res.data
}

// ✅ 正确：完整的错误处理
async loadData() {
  try {
    const res = await api.getData()
    this.data = res.data
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}
```

#### 2. 用户操作必须有反馈
```javascript
// ❌ 错误：没有用户反馈
onItemSelect(item) {
  this.selectedItems.push(item)
}

// ✅ 正确：提供用户反馈
onItemSelect(item) {
  this.selectedItems.push(item)
  uni.showToast({
    title: '已添加到选择',
    icon: 'success'
  })
}
```

### 性能优化验证规则

#### 1. 避免不必要的日志输出
```javascript
// ❌ 错误：生产环境仍有调试日志
onDataChange(e) {
  console.log('详细调试信息:', JSON.stringify(e, null, 2))
  console.log('数据类型:', typeof e)
  console.log('当前状态:', this.currentState)
  // 处理逻辑...
}

// ✅ 正确：只保留必要的错误日志
onDataChange(e) {
  // 处理逻辑...
  // 只在出错时记录日志
  if (error) {
    console.error('数据处理失败:', error)
  }
}
```

#### 2. 防止内存泄漏
```javascript
// ❌ 错误：未清理定时器
onLoad() {
  this.timer = setInterval(() => {
    this.updateData()
  }, 1000)
}

// ✅ 正确：及时清理资源
onLoad() {
  this.timer = setInterval(() => {
    this.updateData()
  }, 1000)
}
onUnload() {
  if (this.timer) {
    clearInterval(this.timer)
    this.timer = null
  }
}
```

## 组件开发规范

### 组件设计原则
1. **单一职责**：每个组件只负责一个功能
2. **可复用性**：组件应该是通用的，可在多个地方使用
3. **可配置性**：通过props提供配置选项
4. **事件通信**：使用事件进行父子组件通信

### 组件模板
```vue
<template>
  <view class="custom-component" :class="customClass">
    <slot name="header"></slot>
    <view class="content">
      <slot></slot>
    </view>
    <slot name="footer"></slot>
  </view>
</template>

<script>
export default {
  name: 'CustomComponent',
  props: {
    // 必需属性
    title: {
      type: String,
      required: true
    },
    // 可选属性
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    // 自定义样式类
    customClass: {
      type: String,
      default: ''
    }
  },
  emits: ['click', 'change'],
  methods: {
    handleClick() {
      this.$emit('click', { /* 事件数据 */ });
    }
  }
}
</script>
```

## 数据管理规范

### 状态管理
```javascript
// store/modules/user.js
const state = {
  userInfo: null,
  isLogin: false
};

const mutations = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
    state.isLogin = !!userInfo;
  },
  
  CLEAR_USER_INFO(state) {
    state.userInfo = null;
    state.isLogin = false;
  }
};

const actions = {
  async login({ commit }, loginData) {
    try {
      const result = await uniCloud.callFunction({
        name: 'user-login',
        data: loginData
      });
      
      if (result.result.status === 'success') {
        commit('SET_USER_INFO', result.result.data);
        return result.result;
      }
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
```

### 本地存储规范
```javascript
// utils/storage.js
class Storage {
  // 设置存储
  static set(key, value, expire = null) {
    const data = {
      value,
      expire: expire ? Date.now() + expire : null
    };
    uni.setStorageSync(key, JSON.stringify(data));
  }
  
  // 获取存储
  static get(key) {
    try {
      const data = JSON.parse(uni.getStorageSync(key) || '{}');
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.remove(key);
        return null;
      }
      
      return data.value;
    } catch (error) {
      return null;
    }
  }
  
  // 删除存储
  static remove(key) {
    uni.removeStorageSync(key);
  }
}

export default Storage;
```

## API调用规范

### 云对象调用规范（官方最佳实践）

1. **目录结构**  
   云对象统一放在 `uniCloud/cloudfunctions/` 目录下，每个云对象为一个独立文件夹，入口为 `index.obj.js`。

2. **方法定义**  
   - 云对象导出的方法必须是普通函数（不能用箭头函数），以保证 `this` 正确指向。
   - 支持定义 `_before`、`_after`、`_timing` 等特殊方法，分别用于预处理、后处理和定时任务。
   - 推荐为每个方法添加 JSDoc 注释，便于 IDE 智能提示。

3. **参数与返回值**  
   - 方法参数直接定义，无需 event 包裹。
   - 返回值推荐遵循 [uniCloud响应体规范](https://doc.dcloud.net.cn/uniCloud/cf-functions.html#resformat)，即包含 `errCode` 和 `errMsg` 字段，便于前端统一处理错误。

4. **权限与拦截**  
   - 可在 `_before` 方法中统一做身份校验、参数校验等拦截处理。
   - `_after` 方法可统一处理返回结果或错误。

5. **客户端调用**  
   - 通过 `uniCloud.importObject('对象名')` 导入云对象实例，直接调用方法，支持 `await`。
   - 推荐使用 `try...catch` 捕获错误，错误对象包含 `errCode`、`errMsg`、`requestId` 等属性。
   - 默认自动显示 loading 和错误弹窗，如需自定义交互，可传入 `{ customUI: true }` 关闭自动 UI。

6. **示例代码**

**云对象服务端示例：**
```js
// uniCloud/cloudfunctions/todo/index.obj.js
module.exports = {
  /**
   * 新增todo
   * @param {string} title 标题
   * @param {string} content 内容
   * @returns {object}
   */
  async add(title = '', content = '') {
    title = title.trim()
    content = content.trim()
    if (!title || !content) {
      return { errCode: 'INVALID_TODO', errMsg: 'TODO标题或内容不可为空' }
    }
    // ...业务逻辑
    return { errCode: 0, errMsg: '创建成功' }
  },
  // 可选：统一身份校验
  _before() {
    // 例如：校验token
    // if (!this.getUniIdToken()) throw new Error('未登录')
  }
}
```

**前端调用示例：**
```js
const todo = uniCloud.importObject('todo')
// 推荐使用try...catch
try {
  const res = await todo.add('标题', '内容')
  // res = { errCode: 0, errMsg: '创建成功' }
} catch (e) {
  // e.errCode, e.errMsg
  uni.showModal({ title: '失败', content: e.errMsg })
}
```
- 如需自定义loading和错误提示：
```js
const todo = uniCloud.importObject('todo', {
  customUI: true // 关闭自动UI，需自行处理loading和错误提示
})
```

7. **注意事项**
   - 云对象方法不可互相调用（可抽离公共函数在模块外部）。
   - 所有 `_` 开头的方法为私有，客户端不可直接调用。
   - 参数体积有上限（阿里云2MB，腾讯云5MB，支付宝云6MB）。
   - 推荐复杂业务用云对象，简单数据库操作可用 clientDB。

## 样式规范

### CSS类命名
- 使用BEM命名规范：`block__element--modifier`
- 组件根元素使用组件名作为类名
- 状态类使用 `is-` 前缀，如 `is-active`、`is-disabled`

### 样式组织
```scss
// styles/variables.scss - 变量定义
$primary-color: #007aff;
$success-color: #4cd964;
$warning-color: #f0ad4e;
$error-color: #dd524d;

$font-size-small: 24rpx;
$font-size-base: 28rpx;
$font-size-large: 32rpx;

// styles/mixins.scss - 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 组件样式
.user-card {
  &__header {
    @include flex-center;
    padding: 20rpx;
  }
  
  &__content {
    padding: 30rpx 20rpx;
  }
  
  &--large {
    .user-card__header {
      padding: 30rpx;
    }
  }
}
```

## 错误处理规范

### 全局错误处理
```javascript
// main.js
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info);
  
  // 上报错误到云端
  uniCloud.callFunction({
    name: 'error-report',
    data: {
      error: err.message,
      stack: err.stack,
      info,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    }
  });
};

// 页面级错误处理
export default {
  methods: {
    async handleAsyncOperation() {
      try {
        const result = await API.user.getUserInfo();
        // 处理成功结果
      } catch (error) {
        this.handleError(error, '获取用户信息失败');
      }
    },
    
    handleError(error, message = '操作失败') {
      console.error(error);
      uni.showToast({
        title: message,
        icon: 'none'
      });
    }
  }
}
```

## 性能优化规范

### 图片优化
- 使用适当的图片格式（WebP > JPEG > PNG）
- 实现图片懒加载
- 使用云存储CDN加速

### 代码优化
- 使用分包加载减少首屏包体积
- 合理使用组件缓存
- 避免在模板中使用复杂计算

### 数据优化
- 实现分页加载
- 使用虚拟列表处理大量数据
- 合理使用缓存策略

## 拓展组件规范

### 组件选择优先级
1. **第一优先级：uv-ui 和 uCharts**
   - **uv-ui**：项目已集成 [uv-ui](https://www.uvui.cn/components/intro.html) UI 组件库
     - 提供丰富的基础 UI 组件，覆盖大部分界面需求
     - 已放置于 `uni-modules` 目录下，可直接使用
   - **uCharts**：项目已集成 [uCharts](https://www.ucharts.cn/v2/#/guide/index) 图表组件库
     - 专门用于数据可视化和图表展示
     - 已放置于 `uni-modules` 目录下，可直接使用

2. **第二优先级：uni-ui 官方组件库**
   - DCloud 官方维护的组件库
   - 稳定性和兼容性较好

3. **第三优先级：uni-modules 生态组件**
   - 社区贡献的扩展组件
   - 功能丰富但需要评估质量

4. **第四优先级：uni-app 内置组件**
   - 框架内置的基础组件
   - 作为最后的选择

5. **组件使用原则**
   - **优先选择 uv-ui 提供的 UI 组件**
   - **图表需求优先选择 uCharts**
   - 参考官方文档，合理调用和配置相关组件
   - 避免重复造轮子，充分利用现有组件生态

### 测试与文档策略
- **测试策略**：uni-app 框架在 VSCode 中无法正常测试，开发过程中不需要编写测试脚本
- **文档策略**：开发阶段专注于功能实现，不需要编写总结文档

## 发布规范

### 版本管理
- 使用语义化版本号：`主版本号.次版本号.修订号`
- 维护详细的更新日志
- 使用Git标签标记版本

### 发布检查清单
- [ ] 代码审查通过
- [ ] 安全检查通过
- [ ] 文档更新完成

## 最佳实践

1. **代码复用**：提取公共组件和工具函数
2. **用户体验**：提供加载状态、错误提示和空状态页面
3. **安全防护**：验证用户输入，防止XSS和CSRF攻击
4. **可维护性**：编写清晰的注释和文档

## 📋 代码修改验证流程

### 修改前验证步骤
1. **需求分析**
   - [ ] 明确修改目标和预期效果
   - [ ] 确认涉及的组件和数据流
   - [ ] 评估修改的影响范围

2. **技术方案验证**
   - [ ] 检查组件兼容性和数据类型匹配
   - [ ] 确认事件处理逻辑的正确性
   - [ ] 验证数据流向和状态管理

3. **代码实现验证**
   - [ ] 遵循命名规范和代码结构
   - [ ] 添加必要的错误处理和用户反馈
   - [ ] 确保性能优化和资源清理

### 修改后验证步骤
1. **功能测试**
   - [ ] 验证核心功能是否正常工作
   - [ ] 测试边界情况和异常处理
   - [ ] 确认用户交互体验

2. **代码质量检查**
   - [ ] 移除调试代码和多余日志
   - [ ] 检查是否有未使用的变量和方法
   - [ ] 确认代码可读性和可维护性

3. **性能和安全检查**
   - [ ] 检查是否有内存泄漏风险
   - [ ] 验证数据验证和安全防护
   - [ ] 确认加载性能和用户体验

## 🛠️ 常见问题解决方案

### 组件多选问题
**问题**：组件不支持真正的多选功能
**解决方案**：
1. 使用单选组件 + 自定义累积逻辑
2. 提供用户操作反馈（Toast提示）
3. 实现选择/取消选择的切换逻辑

### 数据类型不匹配问题
**问题**：v-model 期望的数据类型与实际数据不符
**解决方案**：
1. 明确组件期望的数据类型
2. 使用中间变量进行数据转换
3. 添加数据类型验证和容错处理

### 事件处理问题
**问题**：组件事件返回的数据格式不确定
**解决方案**：
1. 详细记录和分析事件数据格式
2. 添加多种数据格式的兼容处理
3. 使用类型检查确保数据安全

## 代码审查要点

### 基础规范检查
- [ ] 代码是否符合命名规范
- [ ] 组件是否具有良好的复用性
- [ ] 错误处理是否完善
- [ ] 性能是否优化
- [ ] 安全性是否考虑周全
- [ ] 用户体验是否友好

### 深度质量检查
- [ ] 数据流向是否清晰合理
- [ ] 组件选择是否最优
- [ ] 事件处理是否健壮
- [ ] 资源管理是否完善
- [ ] 代码是否易于维护和扩展

