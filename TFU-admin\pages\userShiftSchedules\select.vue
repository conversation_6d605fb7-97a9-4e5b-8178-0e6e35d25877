<template>
  <view class="container">
    <uv-navbar title="选择排班计划" :autoBack="true"></uv-navbar>
    
    <!-- 当前排班信息 -->
    <view class="current-schedule" v-if="currentSchedule">
      <view class="section-title">当前排班计划</view>
      <view class="schedule-card current">
        <view class="schedule-header">
          <text class="schedule-name">{{currentSchedule.scheduleName}}</text>
          <uv-tag text="生效中" type="success" size="mini"></uv-tag>
        </view>
        <view class="schedule-info">
          <text class="info-item">部门：{{currentSchedule.departmentName}}</text>
          <text class="info-item">生效日期：{{formatDate(currentSchedule.effectiveDate)}}</text>
        </view>
      </view>
    </view>

    <!-- 可选排班计划列表 -->
    <view class="available-schedules">
      <view class="section-title">可选排班计划</view>
      <view class="schedule-list">
        <view 
          v-for="schedule in availableSchedules" 
          :key="schedule._id"
          class="schedule-card"
          :class="{ selected: schedule.isSelected }"
          @click="selectSchedule(schedule)">
          
          <view class="schedule-header">
            <text class="schedule-name">{{schedule.name}}</text>
            <uv-tag 
              v-if="schedule.isSelected" 
              text="已选择" 
              type="primary" 
              size="mini">
            </uv-tag>
          </view>
          
          <view class="schedule-description">
            <text>{{schedule.description}}</text>
          </view>
          
          <view class="schedule-info">
            <text class="info-item">开始日期：{{formatDate(schedule.startDate)}}</text>
            <text class="info-item">结束日期：{{formatDate(schedule.endDate)}}</text>
          </view>
          
          <view class="schedule-actions">
            <uv-button 
              type="primary" 
              size="small"
              :disabled="schedule.isSelected"
              @click.stop="confirmSelect(schedule)">
              {{schedule.isSelected ? '已选择' : '选择此计划'}}
            </uv-button>
            <uv-button 
              type="info" 
              size="small" 
              plain
              @click.stop="viewScheduleDetail(schedule)">
              查看详情
            </uv-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="!loading && availableSchedules.length === 0">
      <uv-empty 
        mode="data" 
        text="暂无可选的排班计划"
        textSize="16">
      </uv-empty>
    </view>

    <!-- 加载状态 -->
    <view class="loading" v-if="loading">
      <uv-loading-icon mode="circle"></uv-loading-icon>
      <text>加载中...</text>
    </view>

    <!-- 选择确认弹窗 -->
    <uv-popup ref="confirmPopup" mode="center" :round="10">
      <view class="confirm-popup">
        <view class="popup-title">确认选择排班计划</view>
        <view class="popup-content">
          <text>确定要选择"{{selectedSchedule?.name}}"作为您的排班计划吗？</text>
          <view class="effective-date">
            <text>生效日期：</text>
            <uv-datetime-picker 
              v-model="effectiveDate"
              mode="date"
              :minDate="minDate"
              :maxDate="maxDate">
            </uv-datetime-picker>
          </view>
        </view>
        <view class="popup-actions">
          <uv-button type="info" plain @click="cancelSelect">取消</uv-button>
          <uv-button type="primary" @click="confirmSelectSchedule">确认</uv-button>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      currentSchedule: null,
      availableSchedules: [],
      selectedSchedule: null,
      effectiveDate: new Date(),
      minDate: new Date(),
      maxDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 一年后
      userInfo: null
    }
  },

  async onLoad() {
    await this.getUserInfo()
    await this.loadCurrentSchedule()
    await this.loadAvailableSchedules()
  },

  methods: {
    /**
     * 获取用户信息
     */
    async getUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = userInfo
        } else {
          // 如果本地没有用户信息，跳转到登录页
          uni.redirectTo({
            url: '/pages/login/login'
          })
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    /**
     * 加载当前排班计划
     */
    async loadCurrentSchedule() {
      try {
        const userScheduleObj = uniCloud.importObject('userShiftSchedule')
        const result = await userScheduleObj.getUserSchedule(this.userInfo.userId)
        
        if (result.errCode === 0) {
          this.currentSchedule = result.data
        }
      } catch (error) {
        console.error('加载当前排班失败:', error)
      }
    },

    /**
     * 加载可选排班计划
     */
    async loadAvailableSchedules() {
      if (!this.userInfo?.userId || !this.userInfo?.departmentId) {
        uni.showToast({
          title: '用户信息不完整',
          icon: 'none'
        })
        return
      }

      this.loading = true
      try {
        const userScheduleObj = uniCloud.importObject('userShiftSchedule')
        const result = await userScheduleObj.getAvailableSchedules(
          this.userInfo.userId, 
          this.userInfo.departmentId
        )
        
        if (result.errCode === 0) {
          this.availableSchedules = result.data
        } else {
          uni.showToast({
            title: result.errMsg,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载可选排班计划失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    /**
     * 选择排班计划
     */
    selectSchedule(schedule) {
      if (schedule.isSelected) {
        return
      }
      // 可以在这里添加预览功能
      this.viewScheduleDetail(schedule)
    },

    /**
     * 确认选择排班计划
     */
    confirmSelect(schedule) {
      if (schedule.isSelected) {
        return
      }
      
      this.selectedSchedule = schedule
      this.effectiveDate = new Date()
      this.$refs.confirmPopup.open()
    },

    /**
     * 取消选择
     */
    cancelSelect() {
      this.selectedSchedule = null
      this.$refs.confirmPopup.close()
    },

    /**
     * 确认选择排班计划
     */
    async confirmSelectSchedule() {
      if (!this.selectedSchedule) {
        return
      }

      try {
        uni.showLoading({
          title: '处理中...'
        })

        const userScheduleObj = uniCloud.importObject('userShiftSchedule')
        const result = await userScheduleObj.selectSchedule(
          this.userInfo.userId,
          this.userInfo.departmentId,
          this.selectedSchedule._id,
          this.effectiveDate
        )

        if (result.errCode === 0) {
          uni.showToast({
            title: '选择成功',
            icon: 'success'
          })
          
          // 刷新数据
          await this.loadCurrentSchedule()
          await this.loadAvailableSchedules()
          
          this.$refs.confirmPopup.close()
        } else {
          uni.showToast({
            title: result.errMsg,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('选择排班计划失败:', error)
        uni.showToast({
          title: '选择失败',
          icon: 'none'
        })
      } finally {
        uni.hideLoading()
      }
    },

    /**
     * 查看排班计划详情
     */
    viewScheduleDetail(schedule) {
      uni.navigateTo({
        url: `/pages/simpleShiftSchedules/view?id=${schedule._id}`
      })
    },

    /**
     * 查看我的排班
     */
    viewMySchedule() {
      uni.navigateTo({
        url: '/pages/userShiftSchedules/my'
      })
    },

    /**
     * 格式化日期
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.section-title {
  padding: 20rpx 30rpx 10rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.schedule-card {
  margin: 20rpx 30rpx;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  
  &.current {
    border: 2rpx solid #19be6b;
  }
  
  &.selected {
    border: 2rpx solid #2979ff;
  }
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.schedule-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.schedule-description {
  margin-bottom: 20rpx;
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
}

.schedule-info {
  margin-bottom: 30rpx;
}

.info-item {
  display: block;
  margin-bottom: 10rpx;
  color: #999;
  font-size: 26rpx;
}

.schedule-actions {
  display: flex;
  gap: 20rpx;
}

.empty-state, .loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  color: #999;
}

.confirm-popup {
  width: 600rpx;
  padding: 40rpx;
  background: white;
  border-radius: 16rpx;
}

.popup-title {
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

.popup-content {
  margin-bottom: 40rpx;
  color: #666;
  line-height: 1.5;
}

.effective-date {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
}
</style>
