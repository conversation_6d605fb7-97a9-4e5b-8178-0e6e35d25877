<template>
  <view class="container">
    <uv-navbar title="编辑用户排班" :autoBack="true"></uv-navbar>
    
    <view class="form-container">
      <uv-form ref="form" :model="formData" :rules="rules" labelPosition="top">
        
        <!-- 基础信息 -->
        <view class="form-section">
          <view class="section-title">基础信息</view>
          
          <uv-form-item label="用户名称" prop="userName" required>
            <uv-input 
              v-model="formData.userName" 
              placeholder="请选择用户"
              readonly
              @click="selectUser">
              <template #suffix>
                <uv-icon name="arrow-right" size="16"></uv-icon>
              </template>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="部门名称" prop="departmentName" required>
            <uv-input 
              v-model="formData.departmentName" 
              placeholder="自动获取"
              readonly>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="排班计划" prop="scheduleId" required>
            <uv-input 
              v-model="formData.scheduleName" 
              placeholder="请选择排班计划"
              readonly
              @click="selectSchedule">
              <template #suffix>
                <uv-icon name="arrow-right" size="16"></uv-icon>
              </template>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="状态" prop="status" required>
            <uv-radio-group v-model="formData.status" direction="row">
              <uv-radio 
                v-for="item in statusOptions" 
                :key="item.value"
                :name="item.value"
                :label="item.label">
              </uv-radio>
            </uv-radio-group>
          </uv-form-item>
        </view>

        <!-- 时间设置 -->
        <view class="form-section">
          <view class="section-title">时间设置</view>
          
          <uv-form-item label="生效日期" prop="effectiveDate" required>
            <uv-datetime-picker 
              v-model="formData.effectiveDate"
              mode="date"
              placeholder="请选择生效日期">
            </uv-datetime-picker>
          </uv-form-item>

          <uv-form-item label="失效日期" prop="expiryDate">
            <uv-datetime-picker 
              v-model="formData.expiryDate"
              mode="date"
              placeholder="请选择失效日期">
            </uv-datetime-picker>
          </uv-form-item>
        </view>

        <!-- 继承设置 -->
        <view class="form-section">
          <view class="section-title">继承设置</view>
          
          <uv-form-item label="自动同步">
            <uv-switch 
              v-model="formData.inheritanceSettings.autoSync"
              activeText="开启"
              inactiveText="关闭">
            </uv-switch>
            <view class="form-tip">开启后将自动同步排班计划的更新</view>
          </uv-form-item>

          <uv-form-item label="允许个性化">
            <uv-switch 
              v-model="formData.inheritanceSettings.allowPersonalization"
              activeText="允许"
              inactiveText="禁止">
            </uv-switch>
            <view class="form-tip">允许用户个性化调整排班安排</view>
          </uv-form-item>

          <uv-form-item label="同步排除日期">
            <view class="exclusion-dates">
              <view 
                v-for="(date, index) in formData.inheritanceSettings.syncExclusions" 
                :key="index"
                class="exclusion-item">
                <text class="exclusion-date">{{date}}</text>
                <uv-icon name="close" size="16" @click="removeExclusionDate(index)"></uv-icon>
              </view>
              <uv-button type="info" size="small" plain @click="addExclusionDate">添加排除日期</uv-button>
            </view>
          </uv-form-item>
        </view>

        <!-- 统计信息 -->
        <view class="form-section">
          <view class="section-title">统计信息</view>
          
          <view class="statistics-grid">
            <view class="stat-item">
              <text class="stat-label">工作天数</text>
              <text class="stat-value">{{formData.statistics.totalWorkDays}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">休息天数</text>
              <text class="stat-value">{{formData.statistics.totalRestDays}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">自定义天数</text>
              <text class="stat-value">{{formData.statistics.customizedDays}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">换班申请</text>
              <text class="stat-value">{{formData.statistics.changeRequestCount}}</text>
            </view>
          </view>
        </view>
      </uv-form>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <uv-button type="info" plain @click="goBack">取消</uv-button>
        <uv-button type="primary" @click="submit" :loading="submitting">保存</uv-button>
      </view>
    </view>

    <!-- 用户选择弹窗 -->
    <uv-popup ref="userPopup" mode="bottom" :round="20">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择用户</text>
          <uv-icon name="close" size="20" @click="closeUserPopup"></uv-icon>
        </view>
        <view class="user-list">
          <view 
            v-for="user in userList" 
            :key="user._id"
            class="user-item"
            @click="selectUserItem(user)">
            <view class="user-info">
              <text class="user-name">{{user.nickname || user.username}}</text>
              <text class="user-dept">{{user.departmentName}}</text>
            </view>
            <uv-icon name="arrow-right" size="16"></uv-icon>
          </view>
        </view>
      </view>
    </uv-popup>

    <!-- 排班计划选择弹窗 -->
    <uv-popup ref="schedulePopup" mode="bottom" :round="20">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择排班计划</text>
          <uv-icon name="close" size="20" @click="closeSchedulePopup"></uv-icon>
        </view>
        <view class="schedule-list">
          <view 
            v-for="schedule in availableSchedules" 
            :key="schedule._id"
            class="schedule-item"
            @click="selectScheduleItem(schedule)">
            <view class="schedule-info">
              <text class="schedule-name">{{schedule.name}}</text>
              <text class="schedule-desc">{{schedule.description}}</text>
              <text class="schedule-period">{{formatDate(schedule.startDate)}} - {{formatDate(schedule.endDate)}}</text>
            </view>
            <uv-icon name="arrow-right" size="16"></uv-icon>
          </view>
        </view>
      </view>
    </uv-popup>

    <!-- 日期选择弹窗 -->
    <uv-popup ref="datePopup" mode="center" :round="10">
      <view class="date-popup">
        <view class="popup-title">选择排除日期</view>
        <uv-datetime-picker 
          v-model="selectedExclusionDate"
          mode="date">
        </uv-datetime-picker>
        <view class="popup-actions">
          <uv-button type="info" plain @click="closeDatePopup">取消</uv-button>
          <uv-button type="primary" @click="confirmExclusionDate">确认</uv-button>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      submitting: false,
      recordId: '',
      formData: {
        userId: '',
        userName: '',
        departmentId: '',
        departmentName: '',
        scheduleId: '',
        scheduleName: '',
        status: 'pending',
        effectiveDate: null,
        expiryDate: null,
        inheritanceSettings: {
          autoSync: true,
          allowPersonalization: true,
          syncExclusions: []
        },
        statistics: {
          totalWorkDays: 0,
          totalRestDays: 0,
          customizedDays: 0,
          changeRequestCount: 0
        }
      },
      rules: {
        userName: [
          { required: true, message: '请选择用户', trigger: 'change' }
        ],
        departmentName: [
          { required: true, message: '部门名称不能为空', trigger: 'change' }
        ],
        scheduleId: [
          { required: true, message: '请选择排班计划', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      statusOptions: [
        { label: '待生效', value: 'pending' },
        { label: '生效中', value: 'active' },
        { label: '已停用', value: 'inactive' },
        { label: '已过期', value: 'expired' }
      ],
      userList: [],
      availableSchedules: [],
      selectedExclusionDate: new Date()
    }
  },

  async onLoad(options) {
    if (options.id) {
      this.recordId = options.id
      await this.loadUsers()
      await this.loadRecord()
    }
  },

  methods: {
    /**
     * 加载记录数据
     */
    async loadRecord() {
      try {
        const db = uniCloud.database()
        const res = await db.collection('userShiftSchedules').doc(this.recordId).get()
        
        if (res && res.result && res.result.data.length > 0) {
          const record = res.result.data[0]
          this.formData = {
            ...record,
            inheritanceSettings: record.inheritanceSettings || {
              autoSync: true,
              allowPersonalization: true,
              syncExclusions: []
            },
            statistics: record.statistics || {
              totalWorkDays: 0,
              totalRestDays: 0,
              customizedDays: 0,
              changeRequestCount: 0
            }
          }
          
          // 加载可用排班计划
          if (this.formData.departmentId) {
            await this.loadAvailableSchedules()
          }
        }
      } catch (err) {
        console.error('加载记录失败:', err)
        uni.showToast({
          title: '加载记录失败',
          icon: 'none'
        })
      }
    },

    /**
     * 加载用户列表
     */
    async loadUsers() {
      try {
        const db = uniCloud.database()
        const res = await db.collection('uni-id-users')
          .where({
            status: 0 // 正常状态的用户
          })
          .field({
            _id: true,
            username: true,
            nickname: true,
            department_id: true
          })
          .get()

        if (res && res.result && res.result.data) {
          // 获取部门信息
          const deptRes = await db.collection('opendb-department').get()
          const departments = deptRes.result?.data || []

          this.userList = res.result.data.map(user => {
            const dept = departments.find(d => d._id === user.department_id)
            return {
              ...user,
              departmentId: user.department_id,
              departmentName: dept?.name || '未分配部门'
            }
          })
        }
      } catch (err) {
        console.error('加载用户列表失败:', err)
        uni.showToast({
          title: '加载用户失败',
          icon: 'none'
        })
      }
    },

    /**
     * 加载可用排班计划
     */
    async loadAvailableSchedules() {
      if (!this.formData.departmentId) {
        return
      }

      try {
        const db = uniCloud.database()
        const res = await db.collection('simpleShiftSchedules')
          .where({
            'departments.departmentId': this.formData.departmentId,
            isActive: true
          })
          .get()

        this.availableSchedules = res.result?.data || []
      } catch (err) {
        console.error('加载排班计划失败:', err)
        uni.showToast({
          title: '加载排班计划失败',
          icon: 'none'
        })
      }
    },

    /**
     * 选择用户
     */
    selectUser() {
      this.$refs.userPopup.open()
    },

    /**
     * 选择用户项
     */
    selectUserItem(user) {
      this.formData.userId = user._id
      this.formData.userName = user.nickname || user.username
      this.formData.departmentId = user.departmentId
      this.formData.departmentName = user.departmentName

      this.closeUserPopup()
      this.loadAvailableSchedules()
    },

    /**
     * 关闭用户选择弹窗
     */
    closeUserPopup() {
      this.$refs.userPopup.close()
    },

    /**
     * 选择排班计划
     */
    selectSchedule() {
      if (!this.formData.departmentId) {
        uni.showToast({
          title: '请先选择用户',
          icon: 'none'
        })
        return
      }
      this.$refs.schedulePopup.open()
    },

    /**
     * 选择排班计划项
     */
    selectScheduleItem(schedule) {
      this.formData.scheduleId = schedule._id
      this.formData.scheduleName = schedule.name
      this.closeSchedulePopup()
    },

    /**
     * 关闭排班计划选择弹窗
     */
    closeSchedulePopup() {
      this.$refs.schedulePopup.close()
    },

    /**
     * 添加排除日期
     */
    addExclusionDate() {
      this.selectedExclusionDate = new Date()
      this.$refs.datePopup.open()
    },

    /**
     * 确认排除日期
     */
    confirmExclusionDate() {
      const dateStr = this.formatDate(this.selectedExclusionDate)
      if (!this.formData.inheritanceSettings.syncExclusions.includes(dateStr)) {
        this.formData.inheritanceSettings.syncExclusions.push(dateStr)
      }
      this.closeDatePopup()
    },

    /**
     * 移除排除日期
     */
    removeExclusionDate(index) {
      this.formData.inheritanceSettings.syncExclusions.splice(index, 1)
    },

    /**
     * 关闭日期选择弹窗
     */
    closeDatePopup() {
      this.$refs.datePopup.close()
    },

    /**
     * 提交表单
     */
    async submit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        // 构建提交数据
        const submitData = {
          ...this.formData,
          updatedBy: uni.getStorageSync('userInfo')?.userId || '',
          updatedAt: new Date()
        }

        const db = uniCloud.database()
        await db.collection('userShiftSchedules').doc(this.recordId).update(submitData)

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    },

    /**
     * 返回
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 格式化日期显示
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-container {
  padding: 20rpx 30rpx;
}

.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.exclusion-dates {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  align-items: center;
}

.exclusion-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx 15rpx;
  background: #f0f9ff;
  border-radius: 20rpx;
  border: 1rpx solid #e3f2fd;
}

.exclusion-date {
  font-size: 24rpx;
  color: #333;
}

.statistics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-label {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2979ff;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.popup-content {
  max-height: 80vh;
  padding: 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.user-list, .schedule-list {
  max-height: 60vh;
  overflow-y: auto;
}

.user-item, .schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.user-info, .schedule-info {
  flex: 1;
}

.user-name, .schedule-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.user-dept, .schedule-desc, .schedule-period {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.date-popup {
  width: 600rpx;
  padding: 40rpx;
  background: white;
  border-radius: 16rpx;
}

.popup-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}
</style>
