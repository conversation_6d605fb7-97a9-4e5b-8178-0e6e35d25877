<template>
  <view class="container">
    <uv-navbar title="新增用户排班" :autoBack="true"></uv-navbar>
    
    <view class="form-container">
      <uv-form ref="form" :model="formData" :rules="rules" labelPosition="top">
        
        <!-- 基础信息 -->
        <view class="form-section">
          <view class="section-title">基础信息</view>
          
          <uv-form-item label="用户名称" prop="userName" required>
            <uv-input 
              v-model="formData.userName" 
              placeholder="请选择用户"
              readonly
              @click="selectUser">
              <template #suffix>
                <uv-icon name="arrow-right" size="16"></uv-icon>
              </template>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="部门名称" prop="departmentName" required>
            <uv-input 
              v-model="formData.departmentName" 
              placeholder="自动获取"
              readonly>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="排班计划" prop="scheduleId" required>
            <uv-input 
              v-model="formData.scheduleName" 
              placeholder="请选择排班计划"
              readonly
              @click="selectSchedule">
              <template #suffix>
                <uv-icon name="arrow-right" size="16"></uv-icon>
              </template>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="状态" prop="status" required>
            <uv-radio-group v-model="formData.status" direction="row">
              <uv-radio 
                v-for="item in statusOptions" 
                :key="item.value"
                :name="item.value"
                :label="item.label">
              </uv-radio>
            </uv-radio-group>
          </uv-form-item>
        </view>

        <!-- 时间设置 -->
        <view class="form-section">
          <view class="section-title">时间设置</view>
          
          <uv-form-item label="生效日期" prop="effectiveDate" required>
            <uv-input 
              v-model="effectiveDateStr" 
              placeholder="自动填充"
              readonly>
            </uv-input>
          </uv-form-item>

          <uv-form-item label="失效日期" prop="expiryDate">
            <uv-input 
              v-model="expiryDateStr" 
              placeholder="自动填充"
              readonly>
            </uv-input>
          </uv-form-item>
        </view>

        <!-- 继承设置 -->
        <view class="form-section">
          <view class="section-title">继承设置</view>
          
          <uv-form-item label="自动同步">
            <uv-switch 
              v-model="formData.inheritanceSettings.autoSync"
              activeText="开启"
              inactiveText="关闭">
            </uv-switch>
            <view class="form-tip">开启后将自动同步排班计划的更新</view>
          </uv-form-item>

          <uv-form-item label="允许个性化">
            <uv-switch 
              v-model="formData.inheritanceSettings.allowPersonalization"
              activeText="允许"
              inactiveText="禁止">
            </uv-switch>
            <view class="form-tip">允许用户个性化调整排班安排</view>
          </uv-form-item>
        </view>
      </uv-form>

      <!-- 个人排班数据预览 -->
      <view class="preview-section" v-if="selectedScheduleData">
        <view class="section-title">个人排班数据预览</view>
        <view class="preview-content">
          <view class="preview-header">
            <text class="preview-title">{{formData.scheduleName}}</text>
            <text class="preview-period">{{formatDate(selectedScheduleData.startDate)}} 至 {{formatDate(selectedScheduleData.endDate)}}</text>
          </view>
          
          <view class="preview-calendar">
            <view class="calendar-header">
              <text v-for="day in weekdays" :key="day" class="weekday">{{day}}</text>
            </view>
            <view class="calendar-grid">
              <view 
                v-for="day in previewDays" 
                :key="day.dateStr"
                class="calendar-day"
                :class="{
                  'other-month': day.isOtherMonth,
                  'has-shift': day.hasShift
                }">
                <text class="day-number">{{day.day}}</text>
                <view class="day-shift" v-if="day.shiftInfo">
                  <view 
                    class="shift-tag"
                    :style="{backgroundColor: day.shiftInfo.color}">
                    <text class="shift-name">{{day.shiftInfo.name}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="form-actions">
        <uv-button type="info" plain @click="goBack">取消</uv-button>
        <uv-button type="primary" @click="submit" :loading="submitting">保存</uv-button>
      </view>
    </view>

    <!-- 用户选择弹窗 -->
    <uv-popup ref="userPopup" mode="bottom" :round="20">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择用户</text>
          <uv-icon name="close" size="20" @click="closeUserPopup"></uv-icon>
        </view>
        <view class="user-list">
          <view 
            v-for="user in userList" 
            :key="user._id"
            class="user-item"
            @click="selectUserItem(user)">
            <view class="user-info">
              <text class="user-name">{{user.nickname || user.username}}</text>
              <text class="user-dept">{{user.departmentName}}</text>
            </view>
            <uv-icon name="arrow-right" size="16"></uv-icon>
          </view>
        </view>
      </view>
    </uv-popup>

    <!-- 排班计划选择弹窗 -->
    <uv-popup ref="schedulePopup" mode="bottom" :round="20">
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">选择排班计划</text>
          <uv-icon name="close" size="20" @click="closeSchedulePopup"></uv-icon>
        </view>
        <view class="schedule-list">
          <view 
            v-for="schedule in availableSchedules" 
            :key="schedule._id"
            class="schedule-item"
            @click="selectScheduleItem(schedule)">
            <view class="schedule-info">
              <text class="schedule-name">{{schedule.name}}</text>
              <text class="schedule-desc">{{schedule.description}}</text>
              <text class="schedule-period">{{formatDate(schedule.startDate)}} - {{formatDate(schedule.endDate)}}</text>
            </view>
            <uv-icon name="arrow-right" size="16"></uv-icon>
          </view>
        </view>
      </view>
    </uv-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      submitting: false,
      formData: {
        userId: '',
        userName: '',
        departmentId: '',
        departmentName: '',
        scheduleId: '',
        scheduleName: '',
        status: 'pending',
        effectiveDate: null,
        expiryDate: null,
        inheritanceSettings: {
          autoSync: true,
          allowPersonalization: true,
          syncExclusions: []
        },
        statistics: {
          totalWorkDays: 0,
          totalRestDays: 0,
          customizedDays: 0,
          changeRequestCount: 0
        }
      },
      rules: {
        userName: [
          { required: true, message: '请选择用户', trigger: 'change' }
        ],
        departmentName: [
          { required: true, message: '部门名称不能为空', trigger: 'change' }
        ],
        scheduleId: [
          { required: true, message: '请选择排班计划', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      },
      statusOptions: [
        { label: '待生效', value: 'pending' },
        { label: '生效中', value: 'active' },
        { label: '已停用', value: 'inactive' }
      ],
      userList: [],
      availableSchedules: [],
      selectedScheduleData: null,
      previewDays: [],
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      shiftTypesMap: new Map()
    }
  },

  computed: {
    effectiveDateStr() {
      return this.formData.effectiveDate ? this.formatDate(this.formData.effectiveDate) : ''
    },
    expiryDateStr() {
      return this.formData.expiryDate ? this.formatDate(this.formData.expiryDate) : ''
    }
  },

  async onLoad() {
    await this.loadShiftTypes()
    await this.loadUsers()
  },

  methods: {
    /**
     * 加载班次类型
     */
    async loadShiftTypes() {
      try {
        const db = uniCloud.database()
        const res = await db.collection('simpleShiftTypes').where({
          isActive: true
        }).get()
        
        if (res && res.result && res.result.data) {
          res.result.data.forEach(item => {
            this.shiftTypesMap.set(item.code, {
              name: item.name,
              color: item.display?.color || '#1890ff',
              workTime: item.workTime ? `${item.workTime.startTime}-${item.workTime.endTime}` : ''
            })
          })
        }
      } catch (err) {
        console.error('加载班次类型失败:', err)
      }
    },

    /**
     * 加载用户列表
     */
    async loadUsers() {
      try {
        const db = uniCloud.database()
        const res = await db.collection('uni-id-users')
          .where({
            status: 0 // 正常状态的用户
          })
          .field({
            _id: true,
            username: true,
            nickname: true,
            department_id: true
          })
          .get()

        if (res && res.result && res.result.data) {
          // 获取部门信息
          const deptRes = await db.collection('opendb-department').get()
          const departments = deptRes.result?.data || []
          
          this.userList = res.result.data.map(user => {
            const dept = departments.find(d => d._id === user.department_id)
            return {
              ...user,
              departmentId: user.department_id,
              departmentName: dept?.name || '未分配部门'
            }
          })
        }
      } catch (err) {
        console.error('加载用户列表失败:', err)
        uni.showToast({
          title: '加载用户失败',
          icon: 'none'
        })
      }
    },

    /**
     * 选择用户
     */
    selectUser() {
      this.$refs.userPopup.open()
    },

    /**
     * 选择用户项
     */
    selectUserItem(user) {
      this.formData.userId = user._id
      this.formData.userName = user.nickname || user.username
      this.formData.departmentId = user.departmentId
      this.formData.departmentName = user.departmentName

      // 清空之前选择的排班计划
      this.formData.scheduleId = ''
      this.formData.scheduleName = ''
      this.selectedScheduleData = null
      this.previewDays = []

      this.closeUserPopup()
      this.loadAvailableSchedules()
    },

    /**
     * 关闭用户选择弹窗
     */
    closeUserPopup() {
      this.$refs.userPopup.close()
    },

    /**
     * 加载可用排班计划
     */
    async loadAvailableSchedules() {
      if (!this.formData.departmentId) {
        return
      }

      try {
        const db = uniCloud.database()
        const res = await db.collection('simpleShiftSchedules')
          .where({
            'departments.departmentId': this.formData.departmentId,
            isActive: true
          })
          .get()

        this.availableSchedules = res.result?.data || []
      } catch (err) {
        console.error('加载排班计划失败:', err)
        uni.showToast({
          title: '加载排班计划失败',
          icon: 'none'
        })
      }
    },

    /**
     * 选择排班计划
     */
    selectSchedule() {
      if (!this.formData.departmentId) {
        uni.showToast({
          title: '请先选择用户',
          icon: 'none'
        })
        return
      }
      this.$refs.schedulePopup.open()
    },

    /**
     * 选择排班计划项
     */
    selectScheduleItem(schedule) {
      this.formData.scheduleId = schedule._id
      this.formData.scheduleName = schedule.name
      this.formData.effectiveDate = schedule.startDate ? new Date(schedule.startDate) : new Date()
      this.formData.expiryDate = schedule.endDate ? new Date(schedule.endDate) : null

      this.selectedScheduleData = schedule
      this.generatePreview(schedule)
      this.closeSchedulePopup()
    },

    /**
     * 关闭排班计划选择弹窗
     */
    closeSchedulePopup() {
      this.$refs.schedulePopup.close()
    },

    /**
     * 生成排班预览
     */
    generatePreview(schedule) {
      if (!schedule.scheduleData) {
        this.previewDays = []
        return
      }

      const startDate = new Date(schedule.startDate)
      const endDate = new Date(schedule.endDate)
      const days = []

      // 计算预览的开始日期（包含前面的空白日期）
      const firstDay = new Date(startDate)
      firstDay.setDate(1)
      const previewStart = new Date(firstDay)
      previewStart.setDate(previewStart.getDate() - firstDay.getDay())

      // 生成42天的预览（6周）
      for (let i = 0; i < 42; i++) {
        const date = new Date(previewStart)
        date.setDate(previewStart.getDate() + i)

        const dateStr = this.formatDateStr(date)
        const isOtherMonth = date.getMonth() !== startDate.getMonth()

        // 获取当日排班信息
        const dayData = schedule.scheduleData[dateStr]
        let hasShift = false
        let shiftInfo = null

        if (dayData && dayData.assignments && dayData.assignments[this.formData.departmentId]) {
          hasShift = true
          const assignment = dayData.assignments[this.formData.departmentId]
          const shiftType = this.shiftTypesMap.get(assignment.shiftTypeCode)
          if (shiftType) {
            shiftInfo = {
              name: shiftType.name,
              color: shiftType.color
            }
          }
        }

        days.push({
          day: date.getDate(),
          dateStr: dateStr,
          isOtherMonth: isOtherMonth,
          hasShift: hasShift,
          shiftInfo: shiftInfo
        })
      }

      this.previewDays = days
    },

    /**
     * 提交表单
     */
    async submit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) {
          return
        }

        this.submitting = true

        // 构建提交数据
        const submitData = {
          ...this.formData,
          personalScheduleData: {},
          createdBy: uni.getStorageSync('userInfo')?.userId || '',
          updatedBy: uni.getStorageSync('userInfo')?.userId || '',
          createdAt: new Date(),
          updatedAt: new Date()
        }

        // 继承排班计划数据
        if (this.selectedScheduleData && this.selectedScheduleData.scheduleData) {
          Object.keys(this.selectedScheduleData.scheduleData).forEach(dateStr => {
            const dayData = this.selectedScheduleData.scheduleData[dateStr]
            if (dayData.assignments && dayData.assignments[this.formData.departmentId]) {
              const assignment = dayData.assignments[this.formData.departmentId]
              submitData.personalScheduleData[dateStr] = {
                date: new Date(dateStr),
                weekday: new Date(dateStr).getDay(),
                isHoliday: dayData.isHoliday || false,
                shiftTypeCode: assignment.shiftTypeCode,
                shiftTypeName: assignment.shiftTypeName,
                workTime: assignment.workTime || null,
                isCustomized: false,
                originalShiftTypeCode: assignment.shiftTypeCode,
                notes: ''
              }
            }
          })
        }

        const db = uniCloud.database()
        await db.collection('userShiftSchedules').add(submitData)

        uni.showToast({
          title: '保存成功',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        console.error('保存失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        this.submitting = false
      }
    },

    /**
     * 返回
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 格式化日期字符串
     */
    formatDateStr(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    /**
     * 格式化日期显示
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-container {
  padding: 20rpx 30rpx;
}

.form-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.preview-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.preview-content {
  margin-top: 20rpx;
}

.preview-header {
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.preview-period {
  font-size: 24rpx;
  color: #999;
}

.preview-calendar {
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.calendar-header {
  display: flex;
  background: #f8f9fa;
}

.weekday {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #666;
}

.calendar-grid {
  display: flex;
  flex-wrap: wrap;
}

.calendar-day {
  width: calc(100% / 7);
  min-height: 80rpx;
  padding: 10rpx;
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;

  &.other-month {
    color: #ccc;
    background: #fafafa;
  }

  &.has-shift {
    background: #f0f9ff;
  }
}

.day-number {
  font-size: 24rpx;
  color: #333;
}

.day-shift {
  margin-top: 6rpx;
}

.shift-tag {
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  font-size: 18rpx;
  color: white;
  text-align: center;
}

.shift-name {
  font-size: 18rpx;
}

.form-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}

.popup-content {
  max-height: 80vh;
  padding: 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.user-list, .schedule-list {
  max-height: 60vh;
  overflow-y: auto;
}

.user-item, .schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.user-info, .schedule-info {
  flex: 1;
}

.user-name, .schedule-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.user-dept, .schedule-desc, .schedule-period {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}
</style>
