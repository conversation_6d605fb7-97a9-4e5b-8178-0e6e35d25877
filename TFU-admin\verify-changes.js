/**
 * 验证部门多选功能修改的脚本
 * 检查修改是否正确完成
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const addPagePath = './uni_modules/uni-id-users/pages/uni-id-users/add.vue';
const editPagePath = './uni_modules/uni-id-users/pages/uni-id-users/edit.vue';
const componentPath = './uni_modules/uni-id-users/components/department-multi-select.vue';

console.log('🔍 开始验证部门多选功能修改...\n');

// 验证添加页面
function verifyAddPage() {
  console.log('📄 验证添加页面 (add.vue)...');
  
  if (!fs.existsSync(addPagePath)) {
    console.log('❌ 添加页面文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(addPagePath, 'utf8');
  
  // 检查是否使用了department-multi-select组件
  if (!content.includes('department-multi-select')) {
    console.log('❌ 未找到department-multi-select组件');
    return false;
  }

  // 检查是否导入了组件
  if (!content.includes('DepartmentMultiSelect')) {
    console.log('❌ 未导入DepartmentMultiSelect组件');
    return false;
  }
  
  // 检查是否有handleDepartmentChange方法
  if (!content.includes('handleDepartmentChange')) {
    console.log('❌ 未找到handleDepartmentChange方法');
    return false;
  }
  
  // 检查是否没有排班计划相关代码
  if (content.includes('shifts')) {
    console.log('❌ 仍然包含排班计划相关代码');
    return false;
  }
  
  console.log('✅ 添加页面验证通过');
  return true;
}

// 验证编辑页面
function verifyEditPage() {
  console.log('📄 验证编辑页面 (edit.vue)...');
  
  if (!fs.existsSync(editPagePath)) {
    console.log('❌ 编辑页面文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(editPagePath, 'utf8');
  
  // 检查是否使用了department-multi-select组件
  if (!content.includes('department-multi-select')) {
    console.log('❌ 未找到department-multi-select组件');
    return false;
  }

  // 检查是否导入了组件
  if (!content.includes('DepartmentMultiSelect')) {
    console.log('❌ 未导入DepartmentMultiSelect组件');
    return false;
  }
  
  // 检查是否有handleDepartmentChange方法
  if (!content.includes('handleDepartmentChange')) {
    console.log('❌ 未找到handleDepartmentChange方法');
    return false;
  }
  
  // 检查是否移除了排班计划UI
  if (content.includes('shifts_id') && content.includes('uni-forms-item')) {
    console.log('❌ 仍然包含排班计划UI组件');
    return false;
  }
  
  // 检查是否移除了排班计划方法
  if (content.includes('changeShifts') || content.includes('createSchedule') || content.includes('viewSchedule')) {
    console.log('❌ 仍然包含排班计划相关方法');
    return false;
  }
  
  // 检查是否移除了排班计划常量
  if (content.includes('dbCollectionShifts') || content.includes('dbCollectionUserShifts')) {
    console.log('❌ 仍然包含排班计划相关常量');
    return false;
  }
  
  console.log('✅ 编辑页面验证通过');
  return true;
}

// 验证数据结构
function verifyDataStructure() {
  console.log('📊 验证数据结构...');
  
  const editContent = fs.readFileSync(editPagePath, 'utf8');
  
  // 检查formData中是否正确初始化department_id为数组
  if (!editContent.includes('"department_id": []')) {
    console.log('❌ department_id未正确初始化为数组');
    return false;
  }
  
  // 检查是否移除了formData中的shifts_id
  const formDataMatch = editContent.match(/let formData = \{[\s\S]*?\}/);
  if (formDataMatch && formDataMatch[0].includes('"shifts_id"')) {
    console.log('❌ formData中仍然包含shifts_id字段');
    return false;
  }
  
  console.log('✅ 数据结构验证通过');
  return true;
}

// 验证错误处理
function verifyErrorHandling() {
  console.log('🛡️ 验证错误处理...');
  
  const addContent = fs.readFileSync(addPagePath, 'utf8');
  const editContent = fs.readFileSync(editPagePath, 'utf8');
  
  // 检查是否有try-catch错误处理
  if (!addContent.includes('try {') || !editContent.includes('try {')) {
    console.log('❌ 缺少try-catch错误处理');
    return false;
  }
  
  // 检查是否有用户反馈
  if (!addContent.includes('uni.showToast') || !editContent.includes('uni.showToast')) {
    console.log('❌ 缺少用户反馈提示');
    return false;
  }
  
  console.log('✅ 错误处理验证通过');
  return true;
}

// 主验证函数
function main() {
  const results = [
    verifyAddPage(),
    verifyEditPage(),
    verifyDataStructure(),
    verifyErrorHandling()
  ];
  
  const passedCount = results.filter(r => r).length;
  const totalCount = results.length;
  
  console.log('\n📋 验证结果汇总:');
  console.log(`✅ 通过: ${passedCount}/${totalCount}`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有验证项目都通过了！修改成功完成。');
    console.log('\n📝 下一步建议:');
    console.log('1. 运行测试页面验证功能');
    console.log('2. 检查数据库Schema兼容性');
    console.log('3. 进行用户验收测试');
  } else {
    console.log('⚠️ 部分验证项目未通过，请检查修改。');
  }
}

// 运行验证
main();
