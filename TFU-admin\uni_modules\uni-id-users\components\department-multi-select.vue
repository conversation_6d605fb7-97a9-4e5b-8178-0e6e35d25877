<template>
  <view class="department-multi-select">
    <view class="select-box" :class="{'select-box--disabled': disabled}">
      <view class="select-input" @click="toggleSelector">
        <view v-if="selectedItems.length > 0" class="select-text">
          {{ displayText }}
        </view>
        <view v-else class="select-placeholder">
          {{ placeholder }}
        </view>
        <view v-if="selectedItems.length > 0 && clear && !disabled" class="clear-btn" @click.stop="clearSelection">
          <uni-icons type="clear" color="#c0c4cc" size="20" />
        </view>
        <view v-else class="arrow-btn">
          <uni-icons :type="showSelector ? 'top' : 'bottom'" size="14" color="#999" />
        </view>
      </view>
      
      <!-- 遮罩层 -->
      <view class="select-mask" v-if="showSelector" @click="toggleSelector"></view>
      
      <!-- 下拉选择器 -->
      <view class="select-dropdown" v-if="showSelector">
        <view class="dropdown-arrow"></view>
        <scroll-view scroll-y="true" class="dropdown-scroll">
          <view v-if="loading" class="dropdown-loading">
            <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
          </view>
          <view v-else-if="departmentList.length === 0" class="dropdown-empty">
            <text>{{ emptyText }}</text>
          </view>
          <view v-else>
            <view 
              class="dropdown-item" 
              v-for="item in departmentList" 
              :key="item.value"
              @click="toggleItem(item)">
              <view class="item-content">
                <text class="item-text">{{ item.text }}</text>
                <view class="item-checkbox">
                  <uni-icons 
                    v-if="isSelected(item.value)" 
                    type="checkbox-filled" 
                    color="#007aff" 
                    size="20" />
                  <uni-icons 
                    v-else 
                    type="circle" 
                    color="#ddd" 
                    size="20" />
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DepartmentMultiSelect',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    modelValue: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择部门'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clear: {
      type: Boolean,
      default: true
    },
    emptyText: {
      type: String,
      default: '暂无部门数据'
    },
    loadingText: {
      type: Object,
      default: () => ({
        loading: '加载中...'
      })
    },
    maxDisplay: {
      type: Number,
      default: 2
    }
  },
  emits: ['change', 'update:modelValue', 'input'],
  data() {
    return {
      showSelector: false,
      departmentList: [],
      loading: false,
      selectedItems: []
    }
  },
  computed: {
    currentValue() {
      return this.modelValue || this.value || []
    },
    displayText() {
      if (this.selectedItems.length === 0) return ''
      
      if (this.selectedItems.length <= this.maxDisplay) {
        return this.selectedItems.map(item => item.text).join('、')
      } else {
        const displayItems = this.selectedItems.slice(0, this.maxDisplay)
        const remainCount = this.selectedItems.length - this.maxDisplay
        return displayItems.map(item => item.text).join('、') + ` 等${this.selectedItems.length}个部门`
      }
    }
  },
  watch: {
    currentValue: {
      handler(newVal) {
        this.updateSelectedItems(newVal)
      },
      immediate: true
    }
  },
  mounted() {
    this.loadDepartments()
  },
  methods: {
    /**
     * 加载部门数据
     */
    async loadDepartments() {
      try {
        this.loading = true
        const db = uniCloud.database()
        const res = await db.collection('opendb-department')
          .field('_id as value, name as text')
          .orderBy('name', 'asc')
          .get()
        
        this.departmentList = res.result.data || []
        this.updateSelectedItems(this.currentValue)
      } catch (error) {
        console.error('加载部门数据失败:', error)
        uni.showToast({
          title: '加载部门失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 更新选中项显示
     */
    updateSelectedItems(values) {
      if (!Array.isArray(values) || this.departmentList.length === 0) {
        this.selectedItems = []
        return
      }
      
      this.selectedItems = this.departmentList.filter(dept => 
        values.includes(dept.value)
      )
    },
    
    /**
     * 切换选择器显示状态
     */
    toggleSelector() {
      if (this.disabled) return
      this.showSelector = !this.showSelector
    },
    
    /**
     * 切换选项选中状态
     */
    toggleItem(item) {
      const currentValues = [...this.currentValue]
      const index = currentValues.indexOf(item.value)
      
      if (index > -1) {
        // 取消选中
        currentValues.splice(index, 1)
      } else {
        // 选中
        currentValues.push(item.value)
      }
      
      this.emitChange(currentValues)
    },
    
    /**
     * 判断是否已选中
     */
    isSelected(value) {
      return this.currentValue.includes(value)
    },
    
    /**
     * 清空选择
     */
    clearSelection() {
      this.emitChange([])
    },
    
    /**
     * 发出变更事件
     */
    emitChange(values) {
      this.$emit('input', values)
      this.$emit('update:modelValue', values)
      this.$emit('change', values)
      
      // 显示反馈
      const selectedCount = values.length
      if (selectedCount > 0) {
        uni.showToast({
          title: `已选择${selectedCount}个部门`,
          icon: 'success',
          duration: 1500
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.department-multi-select {
  position: relative;
  width: 100%;
}

.select-box {
  position: relative;
  
  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }
}

.select-input {
  display: flex;
  align-items: center;
  min-height: 70rpx;
  padding: 0 24rpx;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: border-color 0.2s;
  
  &:hover {
    border-color: #c0c4cc;
  }
}

.select-text {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  line-height: 1.4;
  word-break: break-all;
}

.select-placeholder {
  flex: 1;
  color: #c0c4cc;
  font-size: 28rpx;
}

.clear-btn, .arrow-btn {
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 998;
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 8rpx;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  max-height: 400rpx;
}

.dropdown-arrow {
  position: absolute;
  top: -12rpx;
  left: 24rpx;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-bottom: 12rpx solid #fff;
}

.dropdown-scroll {
  max-height: 400rpx;
}

.dropdown-loading, .dropdown-empty {
  padding: 40rpx 24rpx;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.dropdown-item {
  border-bottom: 1px solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f5f7fa;
  }
}

.item-text {
  flex: 1;
  color: #333;
  font-size: 28rpx;
}

.item-checkbox {
  margin-left: 16rpx;
}
</style>
