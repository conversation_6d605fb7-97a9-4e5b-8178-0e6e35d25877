# 用户排班系统集成方案

## 系统概述

用户排班系统是在现有排班计划管理基础上构建的个人排班解决方案，允许用户选择、应用和个性化调整排班计划，实现了从企业级排班管理到个人排班应用的完整闭环。

## 核心功能

### 1. 排班计划选择
- 用户可查看所属部门的可用排班计划
- 支持排班计划的详细预览
- 一键选择并应用排班计划

### 2. 个人排班管理
- 继承排班计划的基础配置
- 支持个性化调整单日排班
- 提供排班统计和分析

### 3. 权限控制
- 用户只能访问自己的排班数据
- 部门级权限控制
- 管理员可管理所有用户排班

### 4. 数据同步
- 自动同步排班计划更新
- 保护用户自定义调整
- 灵活的同步策略配置

## 技术架构

### 数据层
```
simpleShiftSchedules (排班计划)
        ↓ 继承
userShiftSchedules (用户排班)
        ↓ 关联
uni-id-users (用户)
opendb-department (部门)
simpleShiftTypes (班次类型)
```

### 业务层
- **云对象**: `userShiftSchedule` - 提供完整的业务逻辑
- **权限控制**: 基于 uni-id 的用户权限系统
- **数据验证**: 完整的参数验证和错误处理

### 表现层
- **选择页面**: `/pages/userShiftSchedules/select.vue`
- **个人排班**: `/pages/userShiftSchedules/my.vue`
- **个性化调整**: 支持单日排班调整
- **统计分析**: 排班数据统计展示

## 数据库设计

### 核心表结构

#### userShiftSchedules 表
```json
{
  "userId": "用户ID",
  "departmentId": "部门ID", 
  "scheduleId": "排班计划ID",
  "status": "状态(active/pending/inactive/expired)",
  "personalScheduleData": {
    "2024-07-01": {
      "shiftTypeCode": "Day_demo",
      "isCustomized": false,
      "workTime": {...}
    }
  },
  "inheritanceSettings": {
    "autoSync": true,
    "allowPersonalization": true
  },
  "statistics": {
    "totalWorkDays": 22,
    "customizedDays": 2
  }
}
```

### 关键设计特点

1. **动态日期字段**: 使用 `patternProperties` 支持任意日期的排班数据
2. **冗余字段优化**: 存储常用的名称字段，减少关联查询
3. **继承机制**: 清晰区分继承数据和自定义数据
4. **统计信息**: 实时统计排班相关数据

## API 接口设计

### 云对象方法

#### 1. getAvailableSchedules(userId, departmentId)
**功能**: 获取用户可选的排班计划列表
**返回**: 过滤后的排班计划列表，标记已选择状态

#### 2. selectSchedule(userId, departmentId, scheduleId, effectiveDate)
**功能**: 用户选择排班计划
**逻辑**: 
- 验证权限和可用性
- 停用旧排班计划
- 创建新排班记录
- 继承排班数据

#### 3. getUserSchedule(userId, startDate, endDate)
**功能**: 获取用户当前排班数据
**返回**: 完整的个人排班信息和统计数据

#### 4. customizeShift(userId, dateStr, shiftData)
**功能**: 个性化调整单日排班
**逻辑**: 
- 验证调整权限
- 更新排班数据
- 标记自定义状态
- 更新统计信息

#### 5. activateSchedule(userId, userScheduleId)
**功能**: 激活用户排班计划
**逻辑**: 状态管理和权限验证

## 权限控制方案

### 数据访问权限
```javascript
{
  "read": "doc.userId == auth.uid || 'READ_USER_SHIFT_SCHEDULES' in auth.permission",
  "create": "doc.userId == auth.uid || 'CREATE_USER_SHIFT_SCHEDULES' in auth.permission",
  "update": "doc.userId == auth.uid || 'UPDATE_USER_SHIFT_SCHEDULES' in auth.permission",
  "delete": "doc.userId == auth.uid || 'DELETE_USER_SHIFT_SCHEDULES' in auth.permission"
}
```

### 业务权限控制
1. **部门权限**: 用户只能选择所属部门的排班计划
2. **个性化权限**: 根据 `allowPersonalization` 控制调整权限
3. **管理员权限**: 通过特定权限标识管理所有数据

## 与现有系统集成

### 1. 排班计划系统集成
- **数据继承**: 从 `simpleShiftSchedules` 继承基础配置
- **实时同步**: 支持排班计划更新的自动同步
- **兼容性**: 完全兼容现有排班计划结构

### 2. 用户系统集成
- **身份验证**: 基于 uni-id 的用户身份系统
- **部门关联**: 通过 `opendb-department` 实现部门权限
- **角色权限**: 支持不同角色的权限控制

### 3. 班次类型集成
- **类型映射**: 复用 `simpleShiftTypes` 的班次定义
- **颜色显示**: 继承班次类型的颜色配置
- **工作时间**: 自动获取班次的工作时间信息

## 部署和配置

### 1. 数据库部署
```bash
# 1. 上传 schema 文件
uniCloud-alipay/database/userShiftSchedules.schema.json

# 2. 创建索引（可选）
- userId + status
- departmentId + status
- scheduleId
```

### 2. 云函数部署
```bash
# 上传云对象
uniCloud-alipay/cloudfunctions/userShiftSchedule/index.obj.js
```

### 3. 前端页面集成
```bash
# 添加页面文件
pages/userShiftSchedules/select.vue  # 选择排班计划
pages/userShiftSchedules/my.vue      # 我的排班
```

### 4. 路由配置
```json
{
  "pages": [
    {
      "path": "pages/userShiftSchedules/select",
      "style": {
        "navigationBarTitleText": "选择排班计划"
      }
    },
    {
      "path": "pages/userShiftSchedules/my", 
      "style": {
        "navigationBarTitleText": "我的排班"
      }
    }
  ]
}
```

## 使用流程

### 用户首次使用
1. 登录系统，获取用户信息和部门信息
2. 访问排班计划选择页面
3. 查看可用的排班计划列表
4. 选择合适的排班计划并设置生效日期
5. 系统自动继承排班数据并激活

### 日常使用
1. 查看个人排班日历
2. 查看每日排班详情
3. 根据需要进行个性化调整
4. 查看排班统计信息

### 管理员操作
1. 管理用户排班计划分配
2. 监控排班计划使用情况
3. 处理排班相关的申请和审批

## 扩展功能

### 1. 换班申请
- 集成现有的 `shiftSwapRequests` 系统
- 支持用户间的换班申请和审批

### 2. 请假管理
- 扩展个人排班数据支持请假记录
- 集成审批流程

### 3. 统计分析
- 提供更丰富的排班统计功能
- 支持排班效率分析和报表

### 4. 移动端优化
- 优化移动端用户体验
- 支持推送通知

## 最佳实践

1. **数据一致性**: 定期同步排班计划更新，保持数据一致性
2. **性能优化**: 合理使用冗余字段，减少复杂查询
3. **权限控制**: 严格控制数据访问权限，确保数据安全
4. **用户体验**: 提供直观的界面和流畅的操作流程
5. **扩展性**: 为未来功能扩展预留接口和数据结构

## 总结

用户排班系统通过合理的架构设计和数据模型，成功实现了企业排班计划与个人排班需求的有机结合。系统具有良好的扩展性和兼容性，能够满足不同规模企业的排班管理需求，为后续的功能扩展提供了坚实的基础。
