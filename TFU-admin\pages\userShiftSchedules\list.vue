<template>
  <view class="container">
    <uv-navbar title="用户排班管理" :autoBack="true">
      <template #right>
        <uv-icon name="plus" size="20" @click="goToAdd"></uv-icon>
      </template>
    </uv-navbar>

    <!-- 搜索栏 -->
    <view class="search-bar">
      <uv-search 
        v-model="searchKeyword" 
        placeholder="搜索用户名称、部门名称或排班计划"
        @search="handleSearch"
        @clear="handleClear">
      </uv-search>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-bar">
      <uv-dropdown ref="dropdown">
        <uv-dropdown-item 
          v-model="filterStatus" 
          title="状态筛选" 
          :options="statusFilterOptions"
          @change="handleStatusFilter">
        </uv-dropdown-item>
        <uv-dropdown-item 
          v-model="filterDepartment" 
          title="部门筛选" 
          :options="departmentFilterOptions"
          @change="handleDepartmentFilter">
        </uv-dropdown-item>
      </uv-dropdown>
    </view>

    <!-- 列表内容 -->
    <view class="list-container">
      <view class="list-header">
        <text class="total-count">共 {{totalCount}} 条记录</text>
        <view class="batch-actions" v-if="selectedItems.length > 0">
          <text class="selected-count">已选择 {{selectedItems.length}} 项</text>
          <uv-button type="error" size="small" @click="batchDelete">批量删除</uv-button>
        </view>
      </view>

      <view class="schedule-list">
        <view 
          v-for="item in scheduleList" 
          :key="item._id"
          class="schedule-item"
          :class="{ selected: selectedItems.includes(item._id) }"
          @click="toggleSelect(item._id)">
          
          <view class="item-header">
            <view class="user-info">
              <text class="user-name">{{item.userName}}</text>
              <text class="department-name">{{item.departmentName}}</text>
            </view>
            <view class="status-info">
              <uv-tag 
                :text="getStatusText(item.status)" 
                :type="getStatusType(item.status)" 
                size="mini">
              </uv-tag>
            </view>
          </view>

          <view class="item-content">
            <view class="schedule-info">
              <text class="schedule-name">{{item.scheduleName}}</text>
              <text class="schedule-period">
                {{formatDate(item.effectiveDate)}} 
                <text v-if="item.expiryDate">至 {{formatDate(item.expiryDate)}}</text>
              </text>
            </view>
          </view>

          <view class="item-stats">
            <view class="stat-item">
              <text class="stat-label">工作天数</text>
              <text class="stat-value">{{item.statistics?.totalWorkDays || 0}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">自定义</text>
              <text class="stat-value">{{item.statistics?.customizedDays || 0}}</text>
            </view>
            <view class="stat-item">
              <text class="stat-label">换班申请</text>
              <text class="stat-value">{{item.statistics?.changeRequestCount || 0}}</text>
            </view>
          </view>

          <view class="item-actions" @click.stop>
            <uv-button type="primary" size="small" @click="editItem(item)">编辑</uv-button>
            <uv-button type="info" size="small" plain @click="viewDetail(item)">详情</uv-button>
            <uv-button type="error" size="small" plain @click="deleteItem(item)">删除</uv-button>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && scheduleList.length === 0">
        <uv-empty 
          mode="data" 
          text="暂无用户排班记录"
          textSize="16">
        </uv-empty>
        <uv-button type="primary" @click="goToAdd" style="margin-top: 30rpx;">新增用户排班</uv-button>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore && scheduleList.length > 0">
        <uv-loadmore 
          :status="loadMoreStatus" 
          @loadmore="loadMore">
        </uv-loadmore>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading" v-if="loading && scheduleList.length === 0">
      <uv-loading-icon mode="circle"></uv-loading-icon>
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      searchKeyword: '',
      filterStatus: '',
      filterDepartment: '',
      scheduleList: [],
      selectedItems: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      loadMoreStatus: 'loadmore',
      statusFilterOptions: [
        { label: '全部状态', value: '' },
        { label: '待生效', value: 'pending' },
        { label: '生效中', value: 'active' },
        { label: '已停用', value: 'inactive' },
        { label: '已过期', value: 'expired' }
      ],
      departmentFilterOptions: [
        { label: '全部部门', value: '' }
      ]
    }
  },

  async onLoad() {
    await this.loadDepartments()
    await this.loadData()
  },

  onPullDownRefresh() {
    this.refreshData()
  },

  onReachBottom() {
    if (this.hasMore) {
      this.loadMore()
    }
  },

  methods: {
    /**
     * 加载部门列表
     */
    async loadDepartments() {
      try {
        const db = uniCloud.database()
        const res = await db.collection('opendb-department').get()
        
        if (res && res.result && res.result.data) {
          const departments = res.result.data.map(dept => ({
            label: dept.name,
            value: dept._id
          }))
          this.departmentFilterOptions = [
            { label: '全部部门', value: '' },
            ...departments
          ]
        }
      } catch (err) {
        console.error('加载部门列表失败:', err)
      }
    },

    /**
     * 加载数据
     */
    async loadData(isRefresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      if (isRefresh) {
        this.currentPage = 1
        this.scheduleList = []
        this.hasMore = true
      }

      try {
        const db = uniCloud.database()
        let whereCondition = {}
        
        // 搜索条件
        if (this.searchKeyword) {
          whereCondition = db.command.or([
            { userName: new RegExp(this.searchKeyword, 'i') },
            { departmentName: new RegExp(this.searchKeyword, 'i') },
            { scheduleName: new RegExp(this.searchKeyword, 'i') }
          ])
        }
        
        // 状态筛选
        if (this.filterStatus) {
          whereCondition.status = this.filterStatus
        }
        
        // 部门筛选
        if (this.filterDepartment) {
          whereCondition.departmentId = this.filterDepartment
        }

        const res = await db.collection('userShiftSchedules')
          .where(whereCondition)
          .field({
            _id: true,
            userId: true,
            userName: true,
            departmentId: true,
            departmentName: true,
            scheduleId: true,
            scheduleName: true,
            status: true,
            effectiveDate: true,
            expiryDate: true,
            statistics: true,
            createdAt: true,
            updatedAt: true
          })
          .orderBy('createdAt', 'desc')
          .skip((this.currentPage - 1) * this.pageSize)
          .limit(this.pageSize)
          .get()

        if (res && res.result) {
          const newData = res.result.data || []
          
          if (isRefresh) {
            this.scheduleList = newData
          } else {
            this.scheduleList = [...this.scheduleList, ...newData]
          }
          
          this.hasMore = newData.length === this.pageSize
          this.totalCount = res.result.total || this.scheduleList.length
        }

      } catch (err) {
        console.error('加载数据失败:', err)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
        this.loadMoreStatus = 'loadmore'
        
        if (isRefresh) {
          uni.stopPullDownRefresh()
        }
      }
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.loadData(true)
    },

    /**
     * 加载更多
     */
    loadMore() {
      if (!this.hasMore || this.loading) return

      this.loadMoreStatus = 'loading'
      this.currentPage++
      this.loadData()
    },

    /**
     * 搜索处理
     */
    handleSearch() {
      this.refreshData()
    },

    /**
     * 清空搜索
     */
    handleClear() {
      this.searchKeyword = ''
      this.refreshData()
    },

    /**
     * 状态筛选
     */
    handleStatusFilter() {
      this.refreshData()
    },

    /**
     * 部门筛选
     */
    handleDepartmentFilter() {
      this.refreshData()
    },

    /**
     * 切换选择
     */
    toggleSelect(id) {
      const index = this.selectedItems.indexOf(id)
      if (index > -1) {
        this.selectedItems.splice(index, 1)
      } else {
        this.selectedItems.push(id)
      }
    },

    /**
     * 批量删除
     */
    async batchDelete() {
      if (this.selectedItems.length === 0) {
        return
      }

      uni.showModal({
        title: '确认删除',
        content: `确定要删除选中的 ${this.selectedItems.length} 条记录吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const db = uniCloud.database()
              const batch = db.batch()

              this.selectedItems.forEach(id => {
                batch.collection('userShiftSchedules').doc(id).remove()
              })

              await batch.commit()

              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              this.selectedItems = []
              this.refreshData()

            } catch (err) {
              console.error('批量删除失败:', err)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    /**
     * 编辑项目
     */
    editItem(item) {
      uni.navigateTo({
        url: `/pages/userShiftSchedules/edit?id=${item._id}`
      })
    },

    /**
     * 查看详情
     */
    viewDetail(item) {
      uni.navigateTo({
        url: `/pages/userShiftSchedules/detail?id=${item._id}`
      })
    },

    /**
     * 删除项目
     */
    deleteItem(item) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除用户"${item.userName}"的排班记录吗？`,
        success: async (res) => {
          if (res.confirm) {
            try {
              const db = uniCloud.database()
              await db.collection('userShiftSchedules').doc(item._id).remove()

              uni.showToast({
                title: '删除成功',
                icon: 'success'
              })

              this.refreshData()

            } catch (err) {
              console.error('删除失败:', err)
              uni.showToast({
                title: '删除失败',
                icon: 'none'
              })
            }
          }
        }
      })
    },

    /**
     * 去新增页面
     */
    goToAdd() {
      uni.navigateTo({
        url: '/pages/userShiftSchedules/add'
      })
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        'active': '生效中',
        'pending': '待生效',
        'inactive': '已停用',
        'expired': '已过期'
      }
      return statusMap[status] || status
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const typeMap = {
        'active': 'success',
        'pending': 'warning',
        'inactive': 'info',
        'expired': 'error'
      }
      return typeMap[status] || 'info'
    },

    /**
     * 格式化日期显示
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-bar {
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-bar {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.list-container {
  padding: 20rpx 30rpx;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.total-count {
  font-size: 26rpx;
  color: #999;
}

.batch-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.selected-count {
  font-size: 26rpx;
  color: #2979ff;
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.schedule-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  position: relative;

  &.selected {
    border: 2rpx solid #2979ff;
    background: #f0f9ff;
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.department-name {
  font-size: 26rpx;
  color: #666;
}

.item-content {
  margin-bottom: 20rpx;
}

.schedule-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.schedule-period {
  font-size: 24rpx;
  color: #999;
}

.item-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 22rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #2979ff;
}

.item-actions {
  display: flex;
  gap: 15rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
}

.load-more {
  margin-top: 30rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  color: #999;
}
</style>
