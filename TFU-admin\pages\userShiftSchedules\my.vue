<template>
  <view class="container">
    <uv-navbar title="我的排班" :autoBack="true">
      <template #right>
        <uv-icon name="setting" size="20" @click="goToSettings"></uv-icon>
      </template>
    </uv-navbar>

    <!-- 排班计划信息 -->
    <view class="schedule-info" v-if="scheduleInfo">
      <view class="info-card">
        <view class="card-header">
          <text class="schedule-name">{{scheduleInfo.scheduleName}}</text>
          <uv-tag 
            :text="getStatusText(scheduleInfo.status)" 
            :type="getStatusType(scheduleInfo.status)" 
            size="mini">
          </uv-tag>
        </view>
        <view class="card-content">
          <view class="info-row">
            <text class="label">部门：</text>
            <text class="value">{{scheduleInfo.departmentName}}</text>
          </view>
          <view class="info-row">
            <text class="label">生效日期：</text>
            <text class="value">{{formatDate(scheduleInfo.effectiveDate)}}</text>
          </view>
          <view class="info-row" v-if="scheduleInfo.expiryDate">
            <text class="label">失效日期：</text>
            <text class="value">{{formatDate(scheduleInfo.expiryDate)}}</text>
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="statistics">
        <view class="stat-item">
          <text class="stat-number">{{scheduleInfo.statistics?.totalWorkDays || 0}}</text>
          <text class="stat-label">工作天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{scheduleInfo.statistics?.totalRestDays || 0}}</text>
          <text class="stat-label">休息天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{scheduleInfo.statistics?.customizedDays || 0}}</text>
          <text class="stat-label">自定义天数</text>
        </view>
      </view>
    </view>

    <!-- 日历视图 -->
    <view class="calendar-section">
      <view class="calendar-header">
        <uv-button 
          type="info" 
          size="small" 
          plain
          @click="previousMonth">
          <uv-icon name="arrow-left" size="16"></uv-icon>
        </uv-button>
        <text class="month-title">{{currentYear}}年{{currentMonth}}月</text>
        <uv-button 
          type="info" 
          size="small" 
          plain
          @click="nextMonth">
          <uv-icon name="arrow-right" size="16"></uv-icon>
        </uv-button>
      </view>

      <view class="calendar">
        <!-- 星期标题 -->
        <view class="weekdays">
          <text v-for="day in weekdays" :key="day" class="weekday">{{day}}</text>
        </view>

        <!-- 日期网格 -->
        <view class="days-grid">
          <view 
            v-for="day in calendarDays" 
            :key="day.dateStr"
            class="day-cell"
            :class="{
              'other-month': day.isOtherMonth,
              'today': day.isToday,
              'has-shift': day.hasShift,
              'customized': day.isCustomized
            }"
            @click="viewDayDetail(day)">
            
            <text class="day-number">{{day.day}}</text>
            
            <view class="day-shifts" v-if="day.shiftInfo">
              <view 
                class="shift-indicator"
                :style="{backgroundColor: day.shiftInfo.color}">
                <text class="shift-name">{{day.shiftInfo.name}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <uv-button type="primary" @click="goToSelect">更换排班计划</uv-button>
      <uv-button type="info" plain @click="exportSchedule">导出排班</uv-button>
    </view>

    <!-- 日详情弹窗 -->
    <uv-popup ref="dayDetailPopup" mode="bottom" :round="20">
      <view class="day-detail" v-if="selectedDay">
        <view class="detail-header">
          <text class="detail-title">{{selectedDay.dateStr}} 排班详情</text>
          <uv-icon name="close" size="20" @click="closeDayDetail"></uv-icon>
        </view>

        <view class="detail-content" v-if="selectedDay.shiftInfo">
          <view class="shift-detail">
            <view class="shift-name-row">
              <text class="shift-name">{{selectedDay.shiftInfo.name}}</text>
              <uv-tag 
                v-if="selectedDay.isCustomized" 
                text="已自定义" 
                type="warning" 
                size="mini">
              </uv-tag>
            </view>
            
            <view class="shift-time" v-if="selectedDay.shiftInfo.workTime">
              <uv-icon name="clock" size="16"></uv-icon>
              <text>{{selectedDay.shiftInfo.workTime.startTime}} - {{selectedDay.shiftInfo.workTime.endTime}}</text>
            </view>

            <view class="shift-notes" v-if="selectedDay.shiftInfo.notes">
              <text class="notes-label">备注：</text>
              <text class="notes-content">{{selectedDay.shiftInfo.notes}}</text>
            </view>
          </view>

          <view class="detail-actions">
            <uv-button 
              type="warning" 
              size="small"
              @click="customizeShift(selectedDay)">
              个性化调整
            </uv-button>
            <uv-button 
              type="info" 
              size="small" 
              plain
              @click="requestShiftChange(selectedDay)">
              申请换班
            </uv-button>
          </view>
        </view>

        <view class="no-shift" v-else>
          <uv-empty mode="data" text="当日无排班安排"></uv-empty>
        </view>
      </view>
    </uv-popup>

    <!-- 加载状态 -->
    <view class="loading" v-if="loading">
      <uv-loading-icon mode="circle"></uv-loading-icon>
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      scheduleInfo: null,
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      calendarDays: [],
      selectedDay: null,
      weekdays: ['日', '一', '二', '三', '四', '五', '六'],
      userInfo: null,
      shiftTypesMap: new Map()
    }
  },

  async onLoad() {
    await this.getUserInfo()
    await this.loadShiftTypes()
    await this.loadUserSchedule()
    this.generateCalendar()
  },

  methods: {
    /**
     * 获取用户信息
     */
    async getUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo) {
          this.userInfo = userInfo
        } else {
          uni.redirectTo({
            url: '/pages/login/login'
          })
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    },

    /**
     * 加载班次类型
     */
    async loadShiftTypes() {
      try {
        const db = uniCloud.database()
        const res = await db.collection('simpleShiftTypes').where({
          isActive: true
        }).get()
        
        if (res && res.result && res.result.data) {
          res.result.data.forEach(item => {
            this.shiftTypesMap.set(item.code, {
              name: item.name,
              color: item.display?.color || '#1890ff',
              workTime: item.workTime ? `${item.workTime.startTime}-${item.workTime.endTime}` : ''
            })
          })
        }
      } catch (err) {
        console.error('加载班次类型失败:', err)
      }
    },

    /**
     * 加载用户排班数据
     */
    async loadUserSchedule() {
      if (!this.userInfo?.userId) {
        return
      }

      this.loading = true
      try {
        const userScheduleObj = uniCloud.importObject('userShiftSchedule')
        const result = await userScheduleObj.getUserSchedule(this.userInfo.userId)
        
        if (result.errCode === 0) {
          this.scheduleInfo = result.data
        } else if (result.errCode === 'NO_SCHEDULE') {
          // 用户没有排班计划，跳转到选择页面
          uni.showModal({
            title: '提示',
            content: '您还没有选择排班计划，是否现在去选择？',
            success: (res) => {
              if (res.confirm) {
                uni.redirectTo({
                  url: '/pages/userShiftSchedules/select'
                })
              }
            }
          })
        } else {
          uni.showToast({
            title: result.errMsg,
            icon: 'none'
          })
        }
      } catch (error) {
        console.error('加载用户排班失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    /**
     * 生成日历
     */
    generateCalendar() {
      const year = this.currentYear
      const month = this.currentMonth
      const firstDay = new Date(year, month - 1, 1)
      const lastDay = new Date(year, month, 0)
      const startDate = new Date(firstDay)
      startDate.setDate(startDate.getDate() - firstDay.getDay())
      
      const days = []
      const today = new Date()
      
      for (let i = 0; i < 42; i++) {
        const date = new Date(startDate)
        date.setDate(startDate.getDate() + i)
        
        const dateStr = this.formatDateStr(date)
        const isOtherMonth = date.getMonth() !== month - 1
        const isToday = date.toDateString() === today.toDateString()
        
        // 获取当日排班信息
        const shiftData = this.scheduleInfo?.scheduleData?.[dateStr]
        let shiftInfo = null
        let hasShift = false
        let isCustomized = false
        
        if (shiftData) {
          hasShift = true
          isCustomized = shiftData.isCustomized || false
          const shiftType = this.shiftTypesMap.get(shiftData.shiftTypeCode)
          if (shiftType) {
            shiftInfo = {
              name: shiftData.shiftTypeName || shiftType.name,
              color: shiftType.color,
              workTime: shiftData.workTime || shiftType.workTime
            }
          }
        }
        
        days.push({
          day: date.getDate(),
          date: date,
          dateStr: dateStr,
          isOtherMonth: isOtherMonth,
          isToday: isToday,
          hasShift: hasShift,
          isCustomized: isCustomized,
          shiftInfo: shiftInfo,
          shiftData: shiftData
        })
      }
      
      this.calendarDays = days
    },

    /**
     * 上一月
     */
    previousMonth() {
      if (this.currentMonth === 1) {
        this.currentYear--
        this.currentMonth = 12
      } else {
        this.currentMonth--
      }
      this.generateCalendar()
    },

    /**
     * 下一月
     */
    nextMonth() {
      if (this.currentMonth === 12) {
        this.currentYear++
        this.currentMonth = 1
      } else {
        this.currentMonth++
      }
      this.generateCalendar()
    },

    /**
     * 查看日详情
     */
    viewDayDetail(day) {
      this.selectedDay = day
      this.$refs.dayDetailPopup.open()
    },

    /**
     * 关闭日详情
     */
    closeDayDetail() {
      this.$refs.dayDetailPopup.close()
    },

    /**
     * 个性化调整班次
     */
    customizeShift(day) {
      uni.navigateTo({
        url: `/pages/userShiftSchedules/customize?date=${day.dateStr}`
      })
    },

    /**
     * 申请换班
     */
    requestShiftChange(day) {
      uni.navigateTo({
        url: `/pages/shiftSwapRequests/create?date=${day.dateStr}`
      })
    },

    /**
     * 去设置页面
     */
    goToSettings() {
      uni.navigateTo({
        url: '/pages/userShiftSchedules/settings'
      })
    },

    /**
     * 去选择排班计划页面
     */
    goToSelect() {
      uni.navigateTo({
        url: '/pages/userShiftSchedules/select'
      })
    },

    /**
     * 导出排班
     */
    exportSchedule() {
      uni.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        'active': '生效中',
        'pending': '待生效',
        'inactive': '已停用',
        'expired': '已过期'
      }
      return statusMap[status] || status
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const typeMap = {
        'active': 'success',
        'pending': 'warning',
        'inactive': 'info',
        'expired': 'error'
      }
      return typeMap[status] || 'info'
    },

    /**
     * 格式化日期字符串
     */
    formatDateStr(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    /**
     * 格式化日期显示
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.schedule-info {
  padding: 20rpx 30rpx;
}

.info-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.schedule-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
}

.label {
  color: #999;
  font-size: 28rpx;
  width: 140rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  flex: 1;
}

.statistics {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #2979ff;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999;
}

.calendar-section {
  margin: 20rpx 30rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.month-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.weekdays {
  display: flex;
  background: #f8f9fa;
}

.weekday {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #666;
}

.days-grid {
  display: flex;
  flex-wrap: wrap;
}

.day-cell {
  width: calc(100% / 7);
  min-height: 120rpx;
  padding: 10rpx;
  border-right: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
  
  &.other-month {
    color: #ccc;
    background: #fafafa;
  }
  
  &.today {
    background: #e3f2fd;
  }
  
  &.has-shift {
    background: #f0f9ff;
  }
  
  &.customized {
    background: #fff3e0;
  }
}

.day-number {
  font-size: 26rpx;
  color: #333;
}

.day-shifts {
  margin-top: 8rpx;
}

.shift-indicator {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
  color: white;
  text-align: center;
}

.shift-name {
  font-size: 20rpx;
}

.quick-actions {
  padding: 30rpx;
  display: flex;
  gap: 20rpx;
}

.day-detail {
  padding: 40rpx 30rpx;
  max-height: 80vh;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.shift-detail {
  margin-bottom: 30rpx;
}

.shift-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.shift-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.shift-time {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  color: #666;
  font-size: 26rpx;
}

.shift-notes {
  margin-top: 20rpx;
}

.notes-label {
  color: #999;
  font-size: 26rpx;
}

.notes-content {
  color: #333;
  font-size: 26rpx;
  margin-left: 10rpx;
}

.detail-actions {
  display: flex;
  gap: 20rpx;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 30rpx;
  color: #999;
}
</style>
