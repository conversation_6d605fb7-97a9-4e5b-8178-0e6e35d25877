// 表单校验规则由 schema2code 生成，不建议直接修改校验规则，而建议通过 schema2code 生成, 详情: https://uniapp.dcloud.net.cn/uniCloud/schema


const validator = {
  "userId": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "用户ID",
    "label": "用户ID"
  },
  "departmentId": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "部门ID",
    "label": "部门ID"
  },
  "scheduleId": {
    "rules": [
      {
        "required": true
      },
      {
        "format": "string"
      }
    ],
    "title": "排班计划ID",
    "label": "排班计划ID"
  },
  "scheduleName": {
    "rules": [
      {
        "format": "string"
      },
      {
        "maxLength": 100
      }
    ],
    "title": "排班计划名称",
    "label": "排班计划名称"
  },
  "departmentName": {
    "rules": [
      {
        "format": "string"
      },
      {
        "maxLength": 100
      }
    ],
    "title": "部门名称",
    "label": "部门名称"
  },
  "userName": {
    "rules": [
      {
        "format": "string"
      },
      {
        "maxLength": 50
      }
    ],
    "title": "用户姓名",
    "label": "用户姓名"
  },
  "status": {
    "rules": [
      {
        "format": "string"
      },
      {
        "range": [
          {
            "value": "active",
            "text": "active"
          },
          {
            "value": "inactive",
            "text": "inactive"
          },
          {
            "value": "pending",
            "text": "pending"
          },
          {
            "value": "expired",
            "text": "expired"
          }
        ]
      }
    ],
    "title": "状态",
    "defaultValue": "pending",
    "label": "状态"
  },
  "effectiveDate": {
    "rules": [
      {
        "format": "date"
      }
    ],
    "title": "生效日期",
    "label": "生效日期"
  },
  "expiryDate": {
    "rules": [
      {
        "format": "date"
      }
    ],
    "title": "失效日期",
    "label": "失效日期"
  },
  "personalScheduleData": {
    "rules": [
      {
        "format": "object"
      }
    ],
    "title": "个人排班数据",
    "label": "个人排班数据"
  },
  "inheritanceSettings": {
    "rules": [
      {
        "format": "object"
      }
    ],
    "title": "继承设置",
    "label": "继承设置"
  },
  "statistics": {
    "rules": [
      {
        "format": "object"
      }
    ],
    "title": "统计信息",
    "label": "统计信息"
  },
  "createdBy": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "创建人",
    "label": "创建人"
  },
  "updatedBy": {
    "rules": [
      {
        "format": "string"
      }
    ],
    "title": "更新人",
    "label": "更新人"
  },
  "createdAt": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "创建时间",
    "label": "创建时间"
  },
  "updatedAt": {
    "rules": [
      {
        "format": "timestamp"
      }
    ],
    "title": "更新时间",
    "label": "更新时间"
  }
}

const enumConverter = {
  "status_valuetotext": {
    "active": "active",
    "inactive": "inactive",
    "pending": "pending",
    "expired": "expired"
  }
}

function filterToWhere(filter, command) {
  let where = {}
  for (let field in filter) {
    let { type, value } = filter[field]
    switch (type) {
      case "search":
        if (typeof value === 'string' && value.length) {
          where[field] = new RegExp(value)
        }
        break;
      case "select":
        if (value.length) {
          let selectValue = []
          for (let s of value) {
            selectValue.push(command.eq(s))
          }
          where[field] = command.or(selectValue)
        }
        break;
      case "range":
        if (value.length) {
          let gt = value[0]
          let lt = value[1]
          where[field] = command.and([command.gte(gt), command.lte(lt)])
        }
        break;
      case "date":
        if (value.length) {
          let [s, e] = value
          let startDate = new Date(s)
          let endDate = new Date(e)
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
      case "timestamp":
        if (value.length) {
          let [startDate, endDate] = value
          where[field] = command.and([command.gte(startDate), command.lte(endDate)])
        }
        break;
    }
  }
  return where
}

export { validator, enumConverter, filterToWhere }
