/**
 * 用户排班计划云对象
 * 提供用户排班计划的增删改查和业务逻辑
 */
module.exports = {
  /**
   * 获取用户可选的排班计划列表
   * @param {string} userId 用户ID
   * @param {string} departmentId 部门ID
   * @returns {object} 可选的排班计划列表
   */
  async getAvailableSchedules(userId, departmentId) {
    if (!userId || !departmentId) {
      return { errCode: 'INVALID_PARAMS', errMsg: '用户ID和部门ID不能为空' }
    }

    try {
      const db = uniCloud.database()
      
      // 查询该部门可用的排班计划
      const schedules = await db.collection('simpleShiftSchedules')
        .where({
          'departments.departmentId': departmentId,
          isActive: true
        })
        .field({
          _id: true,
          name: true,
          description: true,
          startDate: true,
          endDate: true,
          departments: true
        })
        .get()

      // 查询用户当前的排班计划
      const currentSchedule = await db.collection('userShiftSchedules')
        .where({
          userId: userId,
          status: db.command.in(['active', 'pending'])
        })
        .get()

      const result = schedules.data.map(schedule => ({
        ...schedule,
        isSelected: currentSchedule.data.some(us => us.scheduleId === schedule._id),
        departmentInfo: schedule.departments.find(dept => dept.departmentId === departmentId)
      }))

      return { errCode: 0, errMsg: '获取成功', data: result }
    } catch (error) {
      console.error('获取可选排班计划失败:', error)
      return { errCode: 'QUERY_FAILED', errMsg: '获取排班计划失败' }
    }
  },

  /**
   * 用户选择排班计划
   * @param {string} userId 用户ID
   * @param {string} departmentId 部门ID
   * @param {string} scheduleId 排班计划ID
   * @param {date} effectiveDate 生效日期
   * @returns {object} 操作结果
   */
  async selectSchedule(userId, departmentId, scheduleId, effectiveDate) {
    if (!userId || !departmentId || !scheduleId) {
      return { errCode: 'INVALID_PARAMS', errMsg: '必要参数不能为空' }
    }

    try {
      const db = uniCloud.database()
      
      // 验证排班计划是否存在且可用
      const schedule = await db.collection('simpleShiftSchedules')
        .where({
          _id: scheduleId,
          'departments.departmentId': departmentId,
          isActive: true
        })
        .get()

      if (schedule.data.length === 0) {
        return { errCode: 'SCHEDULE_NOT_FOUND', errMsg: '排班计划不存在或不可用' }
      }

      const scheduleData = schedule.data[0]
      const departmentInfo = scheduleData.departments.find(dept => dept.departmentId === departmentId)

      // 获取用户信息
      const user = await db.collection('uni-id-users').doc(userId).get()
      const department = await db.collection('opendb-department').doc(departmentId).get()

      // 停用用户当前的排班计划
      await db.collection('userShiftSchedules')
        .where({
          userId: userId,
          status: db.command.in(['active', 'pending'])
        })
        .update({
          status: 'inactive',
          updatedAt: new Date(),
          updatedBy: userId
        })

      // 创建新的用户排班记录
      const userSchedule = {
        userId: userId,
        departmentId: departmentId,
        scheduleId: scheduleId,
        scheduleName: scheduleData.name,
        departmentName: department.data?.name || '未知部门',
        userName: user.data?.nickname || user.data?.username || '未知用户',
        status: 'pending',
        effectiveDate: effectiveDate ? new Date(effectiveDate) : new Date(),
        expiryDate: scheduleData.endDate ? new Date(scheduleData.endDate) : null,
        personalScheduleData: {},
        inheritanceSettings: {
          autoSync: true,
          allowPersonalization: true,
          syncExclusions: []
        },
        statistics: {
          totalWorkDays: 0,
          totalRestDays: 0,
          customizedDays: 0,
          changeRequestCount: 0
        },
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // 继承排班计划数据
      if (scheduleData.scheduleData) {
        Object.keys(scheduleData.scheduleData).forEach(dateStr => {
          const dayData = scheduleData.scheduleData[dateStr]
          if (dayData.assignments && dayData.assignments[departmentId]) {
            const assignment = dayData.assignments[departmentId]
            userSchedule.personalScheduleData[dateStr] = {
              date: new Date(dateStr),
              weekday: new Date(dateStr).getDay(),
              isHoliday: dayData.isHoliday || false,
              shiftTypeCode: assignment.shiftTypeCode,
              shiftTypeName: assignment.shiftTypeName,
              workTime: assignment.workTime || null,
              isCustomized: false,
              originalShiftTypeCode: assignment.shiftTypeCode,
              notes: ''
            }
          }
        })
      }

      const result = await db.collection('userShiftSchedules').add(userSchedule)

      return { 
        errCode: 0, 
        errMsg: '选择排班计划成功', 
        data: { 
          userScheduleId: result.id,
          scheduleName: scheduleData.name
        }
      }
    } catch (error) {
      console.error('选择排班计划失败:', error)
      return { errCode: 'SELECT_FAILED', errMsg: '选择排班计划失败' }
    }
  },

  /**
   * 获取用户当前排班数据
   * @param {string} userId 用户ID
   * @param {string} startDate 开始日期 (YYYY-MM-DD)
   * @param {string} endDate 结束日期 (YYYY-MM-DD)
   * @returns {object} 用户排班数据
   */
  async getUserSchedule(userId, startDate, endDate) {
    if (!userId) {
      return { errCode: 'INVALID_PARAMS', errMsg: '用户ID不能为空' }
    }

    try {
      const db = uniCloud.database()
      
      // 获取用户当前生效的排班计划
      const userSchedule = await db.collection('userShiftSchedules')
        .where({
          userId: userId,
          status: 'active'
        })
        .orderBy('effectiveDate', 'desc')
        .limit(1)
        .get()

      if (userSchedule.data.length === 0) {
        return { errCode: 'NO_SCHEDULE', errMsg: '用户暂无生效的排班计划' }
      }

      const schedule = userSchedule.data[0]
      let scheduleData = schedule.personalScheduleData || {}

      // 如果指定了日期范围，过滤数据
      if (startDate && endDate) {
        const filteredData = {}
        Object.keys(scheduleData).forEach(dateStr => {
          if (dateStr >= startDate && dateStr <= endDate) {
            filteredData[dateStr] = scheduleData[dateStr]
          }
        })
        scheduleData = filteredData
      }

      return {
        errCode: 0,
        errMsg: '获取成功',
        data: {
          userScheduleId: schedule._id,
          scheduleName: schedule.scheduleName,
          departmentName: schedule.departmentName,
          status: schedule.status,
          effectiveDate: schedule.effectiveDate,
          expiryDate: schedule.expiryDate,
          scheduleData: scheduleData,
          inheritanceSettings: schedule.inheritanceSettings,
          statistics: schedule.statistics
        }
      }
    } catch (error) {
      console.error('获取用户排班失败:', error)
      return { errCode: 'QUERY_FAILED', errMsg: '获取用户排班失败' }
    }
  },

  /**
   * 用户个性化调整排班
   * @param {string} userId 用户ID
   * @param {string} dateStr 日期字符串 (YYYY-MM-DD)
   * @param {object} shiftData 班次数据
   * @returns {object} 操作结果
   */
  async customizeShift(userId, dateStr, shiftData) {
    if (!userId || !dateStr || !shiftData) {
      return { errCode: 'INVALID_PARAMS', errMsg: '参数不能为空' }
    }

    try {
      const db = uniCloud.database()
      
      // 获取用户当前排班计划
      const userSchedule = await db.collection('userShiftSchedules')
        .where({
          userId: userId,
          status: 'active'
        })
        .get()

      if (userSchedule.data.length === 0) {
        return { errCode: 'NO_SCHEDULE', errMsg: '用户暂无生效的排班计划' }
      }

      const schedule = userSchedule.data[0]
      
      // 检查是否允许个性化调整
      if (!schedule.inheritanceSettings.allowPersonalization) {
        return { errCode: 'PERSONALIZATION_DISABLED', errMsg: '当前排班计划不允许个性化调整' }
      }

      // 构建更新数据
      const updateData = {
        [`personalScheduleData.${dateStr}`]: {
          ...shiftData,
          isCustomized: true,
          date: new Date(dateStr),
          weekday: new Date(dateStr).getDay()
        },
        updatedAt: new Date(),
        updatedBy: userId
      }

      // 更新统计信息
      if (!schedule.personalScheduleData[dateStr]?.isCustomized) {
        updateData['statistics.customizedDays'] = db.command.inc(1)
      }

      await db.collection('userShiftSchedules')
        .doc(schedule._id)
        .update(updateData)

      return { errCode: 0, errMsg: '个性化调整成功' }
    } catch (error) {
      console.error('个性化调整失败:', error)
      return { errCode: 'CUSTOMIZE_FAILED', errMsg: '个性化调整失败' }
    }
  },

  /**
   * 激活用户排班计划
   * @param {string} userId 用户ID
   * @param {string} userScheduleId 用户排班计划ID
   * @returns {object} 操作结果
   */
  async activateSchedule(userId, userScheduleId) {
    if (!userId || !userScheduleId) {
      return { errCode: 'INVALID_PARAMS', errMsg: '参数不能为空' }
    }

    try {
      const db = uniCloud.database()
      
      // 验证排班计划归属
      const schedule = await db.collection('userShiftSchedules')
        .where({
          _id: userScheduleId,
          userId: userId,
          status: 'pending'
        })
        .get()

      if (schedule.data.length === 0) {
        return { errCode: 'SCHEDULE_NOT_FOUND', errMsg: '排班计划不存在或已激活' }
      }

      // 停用其他排班计划
      await db.collection('userShiftSchedules')
        .where({
          userId: userId,
          status: 'active'
        })
        .update({
          status: 'inactive',
          updatedAt: new Date(),
          updatedBy: userId
        })

      // 激活当前排班计划
      await db.collection('userShiftSchedules')
        .doc(userScheduleId)
        .update({
          status: 'active',
          updatedAt: new Date(),
          updatedBy: userId
        })

      return { errCode: 0, errMsg: '排班计划激活成功' }
    } catch (error) {
      console.error('激活排班计划失败:', error)
      return { errCode: 'ACTIVATE_FAILED', errMsg: '激活排班计划失败' }
    }
  }
}
