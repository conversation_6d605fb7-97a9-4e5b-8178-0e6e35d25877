# 用户排班计划数据库设计文档

## 概述

`userShiftSchedules` 表是用户个人排班系统的核心数据表，用于建立用户与排班计划之间的关联关系，支持用户选择、应用和个性化调整排班计划。

## 设计目标

1. **用户排班关联**：建立用户与排班计划的关联关系
2. **权限控制**：确保用户只能访问和修改自己的排班数据
3. **数据继承**：支持从排班计划继承配置并允许个性化调整
4. **扩展性**：为未来的换班申请、请假等功能预留接口

## 核心字段说明

### 基础关联字段
- `userId`: 用户ID，关联 `uni-id-users` 表
- `departmentId`: 部门ID，关联 `opendb-department` 表
- `scheduleId`: 排班计划ID，关联 `simpleShiftSchedules` 表
- `status`: 排班状态（pending/active/inactive/expired）

### 冗余字段（提升查询性能）
- `scheduleName`: 排班计划名称
- `departmentName`: 部门名称  
- `userName`: 用户姓名

### 时间控制字段
- `effectiveDate`: 生效日期
- `expiryDate`: 失效日期

## 核心功能设计

### 1. 个人排班数据 (personalScheduleData)

采用动态字段设计，以日期为key存储每日排班：

```json
{
  "personalScheduleData": {
    "2024-07-01": {
      "date": "2024-07-01T00:00:00.000Z",
      "weekday": 1,
      "shiftTypeCode": "Day_demo",
      "shiftTypeName": "白班",
      "workTime": {
        "startTime": "08:00",
        "endTime": "17:00",
        "isOvernight": false
      },
      "isCustomized": false,
      "originalShiftTypeCode": "Day_demo",
      "notes": "正常排班"
    }
  }
}
```

**关键特性**：
- 支持用户个性化调整（`isCustomized` 标记）
- 保留原始排班信息（`originalShiftTypeCode`）
- 支持跨夜班次（`isOvernight`）
- 节假日标记（`isHoliday`）

### 2. 继承设置 (inheritanceSettings)

控制如何从排班计划继承数据：

```json
{
  "inheritanceSettings": {
    "autoSync": true,
    "allowPersonalization": true,
    "syncExclusions": ["2024-07-15", "2024-07-16"]
  }
}
```

**功能说明**：
- `autoSync`: 是否自动同步排班计划更新
- `allowPersonalization`: 是否允许用户个性化调整
- `syncExclusions`: 排除自动同步的日期列表

### 3. 统计信息 (statistics)

提供排班统计数据：

```json
{
  "statistics": {
    "totalWorkDays": 22,
    "totalRestDays": 8,
    "customizedDays": 2,
    "changeRequestCount": 1
  }
}
```

## 权限控制设计

### 数据访问权限
```json
{
  "permission": {
    "read": "doc.userId == auth.uid || 'READ_USER_SHIFT_SCHEDULES' in auth.permission",
    "create": "doc.userId == auth.uid || 'CREATE_USER_SHIFT_SCHEDULES' in auth.permission",
    "update": "doc.userId == auth.uid || 'UPDATE_USER_SHIFT_SCHEDULES' in auth.permission",
    "delete": "doc.userId == auth.uid || 'DELETE_USER_SHIFT_SCHEDULES' in auth.permission"
  }
}
```

**权限说明**：
- 用户只能访问自己的排班数据（`doc.userId == auth.uid`）
- 管理员可通过特定权限访问所有数据
- 支持细粒度的CRUD权限控制

## 与现有系统的集成

### 1. 与 simpleShiftSchedules 的关系

```mermaid
graph LR
    A[simpleShiftSchedules] -->|继承| B[userShiftSchedules]
    B -->|个性化| C[个人排班数据]
    D[用户] -->|选择| A
    D -->|应用| B
```

**集成要点**：
- 用户从可用的排班计划中选择
- 继承排班计划的基础配置
- 支持个性化调整而不影响原计划

### 2. 与用户系统的集成

- 通过 `userId` 关联用户表
- 通过 `departmentId` 确保部门权限
- 支持用户角色和权限验证

### 3. 数据同步机制

```javascript
// 示例：从排班计划同步数据
async function syncFromSchedule(userScheduleId, scheduleId) {
  const schedule = await db.collection('simpleShiftSchedules').doc(scheduleId).get()
  const userSchedule = await db.collection('userShiftSchedules').doc(userScheduleId).get()
  
  // 同步逻辑
  const syncData = {}
  Object.keys(schedule.scheduleData).forEach(date => {
    // 跳过用户自定义的日期
    if (!userSchedule.inheritanceSettings.syncExclusions.includes(date)) {
      syncData[date] = {
        ...schedule.scheduleData[date],
        isCustomized: false,
        originalShiftTypeCode: schedule.scheduleData[date].shiftTypeCode
      }
    }
  })
  
  return syncData
}
```

## 使用场景

### 1. 用户选择排班计划
```javascript
// 创建用户排班关联
const userSchedule = {
  userId: "user123",
  departmentId: "dept456", 
  scheduleId: "schedule789",
  status: "pending",
  effectiveDate: new Date("2024-07-01"),
  inheritanceSettings: {
    autoSync: true,
    allowPersonalization: true
  }
}
```

### 2. 个性化调整排班
```javascript
// 用户调整某日排班
const customization = {
  "personalScheduleData.2024-07-15": {
    shiftTypeCode: "Rest_demo",
    shiftTypeName: "休息",
    isCustomized: true,
    originalShiftTypeCode: "Day_demo",
    notes: "个人请假"
  }
}
```

### 3. 查询用户排班
```javascript
// 查询用户当前生效的排班
const userSchedule = await db.collection('userShiftSchedules')
  .where({
    userId: "user123",
    status: "active",
    effectiveDate: db.command.lte(new Date()),
    expiryDate: db.command.gte(new Date())
  })
  .get()
```

## 扩展功能预留

### 1. 换班申请支持
- 预留了 `changeRequests` 字段结构
- 支持与 `shiftSwapRequests` 表集成

### 2. 审批流程集成
- 可与现有审批系统集成
- 支持排班变更的审批流程

### 3. 统计分析
- 提供丰富的统计字段
- 支持排班效率分析

## 最佳实践建议

1. **数据一致性**：定期同步排班计划更新
2. **性能优化**：合理使用冗余字段减少关联查询
3. **权限控制**：严格控制用户只能访问自己的数据
4. **扩展性**：为未来功能预留字段和接口
5. **数据备份**：重要的排班变更应保留历史记录

## 总结

`userShiftSchedules` 表设计充分考虑了用户排班的复杂需求，既保证了与现有系统的兼容性，又为未来功能扩展提供了良好的基础。通过合理的权限控制和数据结构设计，能够满足企业级排班管理的各种需求。
