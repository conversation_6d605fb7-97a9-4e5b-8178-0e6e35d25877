{"bsonType": "object", "required": ["userId", "departmentId", "scheduleId"], "permission": {"read": "doc.userId == auth.uid || 'READ_USER_SHIFT_SCHEDULES' in auth.permission", "create": "doc.userId == auth.uid || 'CREATE_USER_SHIFT_SCHEDULES' in auth.permission", "update": "doc.userId == auth.uid || 'UPDATE_USER_SHIFT_SCHEDULES' in auth.permission", "delete": "doc.userId == auth.uid || 'DELETE_USER_SHIFT_SCHEDULES' in auth.permission"}, "properties": {"_id": {"bsonType": "objectId", "description": "ID，系统自动生成"}, "userId": {"bsonType": "string", "title": "用户ID", "description": "关联的用户ID", "foreignKey": "uni-id-users._id"}, "departmentId": {"bsonType": "string", "title": "部门ID", "description": "用户所属部门ID", "foreignKey": "opendb-department._id"}, "scheduleId": {"bsonType": "string", "title": "排班计划ID", "description": "用户选择的排班计划ID", "foreignKey": "simpleShiftSchedules._id"}, "scheduleName": {"bsonType": "string", "title": "排班计划名称", "description": "排班计划的名称（冗余字段，便于查询显示）", "maxLength": 100}, "departmentName": {"bsonType": "string", "title": "部门名称", "description": "部门名称（冗余字段，便于查询显示）", "maxLength": 100}, "userName": {"bsonType": "string", "title": "用户姓名", "description": "用户姓名（冗余字段，便于查询显示）", "maxLength": 50}, "status": {"bsonType": "string", "title": "状态", "description": "用户排班状态", "enum": ["active", "inactive", "pending", "expired"], "enumDesc": ["生效中", "已停用", "待生效", "已过期"], "defaultValue": "pending"}, "effectiveDate": {"bsonType": "date", "title": "生效日期", "description": "排班计划对用户的生效日期"}, "expiryDate": {"bsonType": "date", "title": "失效日期", "description": "排班计划对用户的失效日期"}, "personalScheduleData": {"bsonType": "object", "title": "个人排班数据", "description": "用户的个人排班数据，继承自排班计划并可个性化调整", "patternProperties": {"^\\d{4}-\\d{2}-\\d{2}$": {"bsonType": "object", "title": "单日个人排班", "description": "特定日期的个人排班安排", "properties": {"date": {"bsonType": "date", "title": "日期"}, "weekday": {"bsonType": "int", "title": "星期几", "minimum": 0, "maximum": 6}, "isHoliday": {"bsonType": "bool", "title": "是否节假日", "defaultValue": false}, "shiftTypeCode": {"bsonType": "string", "title": "班次类型编码", "description": "用户当天的班次类型", "foreignKey": "simpleShiftTypes.code"}, "shiftTypeName": {"bsonType": "string", "title": "班次类型名称", "description": "班次类型名称（冗余字段）"}, "workTime": {"bsonType": "object", "title": "工作时间", "description": "具体的工作时间安排", "properties": {"startTime": {"bsonType": "string", "title": "开始时间", "description": "工作开始时间，格式：HH:mm"}, "endTime": {"bsonType": "string", "title": "结束时间", "description": "工作结束时间，格式：HH:mm"}, "isOvernight": {"bsonType": "bool", "title": "是否跨夜", "description": "工作时间是否跨越午夜", "defaultValue": false}}}, "isCustomized": {"bsonType": "bool", "title": "是否自定义", "description": "该日排班是否为用户自定义（区别于继承自排班计划）", "defaultValue": false}, "originalShiftTypeCode": {"bsonType": "string", "title": "原始班次类型", "description": "继承自排班计划的原始班次类型（用于换班申请时的对比）"}, "notes": {"bsonType": "string", "title": "备注", "description": "该日排班的备注信息", "maxLength": 200}}}}}, "inheritanceSettings": {"bsonType": "object", "title": "继承设置", "description": "从排班计划继承的设置选项", "properties": {"autoSync": {"bsonType": "bool", "title": "自动同步", "description": "是否自动同步排班计划的更新", "defaultValue": true}, "allowPersonalization": {"bsonType": "bool", "title": "允许个性化", "description": "是否允许用户个性化调整排班", "defaultValue": true}, "syncExclusions": {"bsonType": "array", "title": "同步排除", "description": "不参与自动同步的日期列表", "items": {"bsonType": "string", "description": "日期字符串，格式：YYYY-MM-DD"}}}}, "statistics": {"bsonType": "object", "title": "统计信息", "description": "用户排班的统计数据", "properties": {"totalWorkDays": {"bsonType": "int", "title": "总工作天数", "description": "当前排班计划期间的总工作天数", "minimum": 0, "defaultValue": 0}, "totalRestDays": {"bsonType": "int", "title": "总休息天数", "description": "当前排班计划期间的总休息天数", "minimum": 0, "defaultValue": 0}, "customizedDays": {"bsonType": "int", "title": "自定义天数", "description": "用户自定义调整的天数", "minimum": 0, "defaultValue": 0}, "changeRequestCount": {"bsonType": "int", "title": "换班申请次数", "description": "用户提交的换班申请总次数", "minimum": 0, "defaultValue": 0}}}, "createdBy": {"bsonType": "string", "title": "创建人", "description": "创建该记录的用户ID", "foreignKey": "uni-id-users._id"}, "updatedBy": {"bsonType": "string", "title": "更新人", "description": "最后更新该记录的用户ID", "foreignKey": "uni-id-users._id"}, "createdAt": {"bsonType": "timestamp", "title": "创建时间", "description": "记录创建时间", "forceDefaultValue": {"$env": "now"}}, "updatedAt": {"bsonType": "timestamp", "title": "更新时间", "description": "记录最后更新时间", "forceDefaultValue": {"$env": "now"}}}}